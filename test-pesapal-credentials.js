// Test script to verify Pesapal credentials and API integration
// This will help us debug the payment issues

async function testPesapalCredentials() {
  // Using the correct demo credentials from Pesapal
  const DEMO_CONSUMER_KEY = 'qkio1BGGYAXTu2JOfm7XSXNruoZsrqEW';
  const DEMO_CONSUMER_SECRET = 'osGQ364R49cXKeOYSpaOnT++rHs=';
  
  console.log('Testing Pesapal API with demo credentials...');
  
  try {
    // Step 1: Test token request
    console.log('Step 1: Requesting access token...');
    const tokenResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Auth/RequestToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        consumer_key: DEMO_CONSUMER_KEY,
        consumer_secret: DEMO_CONSUMER_SECRET
      })
    });

    console.log('Token response status:', tokenResponse.status);
    
    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Token request failed:', errorText);
      
      // Try to parse as JSON to get more details
      try {
        const errorJson = JSON.parse(errorText);
        console.error('Parsed error:', errorJson);
      } catch (e) {
        console.error('Could not parse error as JSON');
      }
      return;
    }

    const tokenData = await tokenResponse.json();
    console.log('Token response:', tokenData);
    
    if (!tokenData.token) {
      console.error('No token in response');
      return;
    }

    const accessToken = tokenData.token;
    console.log('✓ Access token obtained successfully');

    // Step 2: Test IPN registration
    console.log('\nStep 2: Testing IPN registration...');
    const ipnResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/URLSetup/RegisterIPN', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        url: 'https://oijoqbyfwbyochcxdutg.supabase.co/functions/v1/pesapal-webhook',
        ipn_notification_type: 'GET'
      })
    });

    console.log('IPN response status:', ipnResponse.status);
    const ipnData = await ipnResponse.json();
    console.log('IPN response:', ipnData);

    // Step 3: Test order submission with KES
    console.log('\nStep 3: Testing order submission with KES...');
    const orderData = {
      id: `test_order_${Date.now()}`,
      currency: 'KES',
      amount: 10,
      description: 'Test Payment - vertiQ Business Management Software',
      callback_url: 'https://oijoqbyfwbyochcxdutg.supabase.co/functions/v1/pesapal-callback',
      notification_id: ipnData.ipn_id || '',
      billing_address: {
        email_address: '<EMAIL>',
        phone_number: '+254700000000',
        country_code: 'KE',
        first_name: 'Test',
        last_name: 'User',
        line_1: 'Test Address',
        line_2: '',
        city: 'Nairobi',
        state: 'Nairobi',
        postal_code: '00100',
        zip_code: '00100'
      }
    };

    console.log('Order data:', JSON.stringify(orderData, null, 2));

    const orderResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Transactions/SubmitOrderRequest', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify(orderData)
    });

    console.log('Order response status:', orderResponse.status);
    
    if (!orderResponse.ok) {
      const errorText = await orderResponse.text();
      console.error('Order submission failed:', errorText);
      
      try {
        const errorJson = JSON.parse(errorText);
        console.error('Parsed order error:', errorJson);
      } catch (e) {
        console.error('Could not parse order error as JSON');
      }
      return;
    }

    const orderResult = await orderResponse.json();
    console.log('Order result:', orderResult);

    if (orderResult.redirect_url) {
      console.log('✓ SUCCESS: Payment URL generated:', orderResult.redirect_url);
      console.log('✓ Tracking ID:', orderResult.tracking_id);
    } else {
      console.error('✗ No redirect URL in response');
    }

  } catch (error) {
    console.error('Test failed with exception:', error);
  }
}

// Test different currency options
async function testCurrencySupport() {
  console.log('\n=== Testing Currency Support ===');
  
  const currencies = ['KES', 'USD', 'UGX', 'TZS'];
  
  for (const currency of currencies) {
    console.log(`\nTesting ${currency}...`);
    // This is just a structure test - we won't actually submit orders
    const testOrder = {
      currency: currency,
      amount: currency === 'KES' ? 10 : (currency === 'USD' ? 0.10 : 100)
    };
    console.log(`Test order structure for ${currency}:`, testOrder);
  }
}

// Run the tests
console.log('=== Pesapal Integration Test ===');
testPesapalCredentials().then(() => {
  testCurrencySupport();
});
