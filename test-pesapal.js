// Test script to debug Pesapal integration
const PESAPAL_CONSUMER_KEY = 'qkio1BGGYAXTu2JOfm7XSXNjwcwpBw10'; // Test key from Pesapal docs
const PESAPAL_CONSUMER_SECRET = 'osGQ364R2u8RZOqwXCaak7XL8Gm5EYzs'; // Test secret from Pesapal docs

async function testPesapalAPI() {
  try {
    console.log('Testing Pesapal API...');
    
    // Step 1: Get access token
    console.log('Step 1: Getting access token...');
    const tokenResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Auth/RequestToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        consumer_key: PESAPAL_CONSUMER_KEY,
        consumer_secret: PESAPAL_CONSUMER_SECRET
      })
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Token request failed:', tokenResponse.status, errorText);
      return;
    }

    const tokenData = await tokenResponse.json();
    console.log('Token response:', tokenData);
    
    if (!tokenData.token) {
      console.error('No token in response');
      return;
    }

    const accessToken = tokenData.token;
    console.log('Access token obtained successfully');

    // Step 2: Register IPN URL
    console.log('Step 2: Registering IPN URL...');
    const ipnResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/URLSetup/RegisterIPN', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        url: 'https://oijoqbyfwbyochcxdutg.supabase.co/functions/v1/pesapal-webhook',
        ipn_notification_type: 'GET'
      })
    });

    const ipnData = await ipnResponse.json();
    console.log('IPN Registration response:', ipnData);

    // Step 3: Submit order
    console.log('Step 3: Submitting test order...');
    const orderData = {
      id: `test_order_${Date.now()}`,
      currency: 'USD',
      amount: 0.10,
      description: 'Test Payment - vertiQ Business Management Software',
      callback_url: 'https://oijoqbyfwbyochcxdutg.supabase.co/functions/v1/pesapal-callback',
      notification_id: ipnData.ipn_id || '',
      billing_address: {
        email_address: '<EMAIL>',
        phone_number: '+254700000000',
        country_code: 'KE',
        first_name: 'Test',
        last_name: 'User',
        line_1: 'Test Address',
        line_2: '',
        city: 'Nairobi',
        state: 'Nairobi',
        postal_code: '00100',
        zip_code: '00100'
      }
    };

    console.log('Order data:', JSON.stringify(orderData, null, 2));

    const orderResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Transactions/SubmitOrderRequest', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify(orderData)
    });

    console.log('Order response status:', orderResponse.status);
    
    if (!orderResponse.ok) {
      const errorText = await orderResponse.text();
      console.error('Order submission failed:', errorText);
      return;
    }

    const orderResult = await orderResponse.json();
    console.log('Order result:', orderResult);

    if (orderResult.redirect_url) {
      console.log('SUCCESS: Payment URL generated:', orderResult.redirect_url);
    } else {
      console.error('No redirect URL in response');
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testPesapalAPI();
