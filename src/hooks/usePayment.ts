
import { useState } from 'react'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'

export const usePayment = () => {
  const [loading, setLoading] = useState(false)
  const [hasStartedPayment, setHasStartedPayment] = useState(false)
  const { user } = useAuth()
  const { toast } = useToast()

  const checkPaymentStatus = async () => {
    if (!user) return false

    try {
      const { data: settings } = await supabase
        .from('user_settings')
        .select('has_paid')
        .eq('user_id', user.id)
        .single()

      return settings?.has_paid || false
    } catch (error) {
      console.error('Error checking payment status:', error)
      return false
    }
  }

  const initiatePayment = async () => {
    if (!user) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Please log in to make payment'
      })
      return
    }

    setLoading(true)
    setHasStartedPayment(true)

    try {
      console.log('Initiating payment for user:', user.id)
      
      const { data, error } = await supabase.functions.invoke('pesapal-initiate', {
        body: {
          amount: 10, // 10 KES (approximately 0.10 USD)
          currency: 'KES'
        }
      })

      console.log('Payment initiation response:', { data, error })

      if (error) {
        console.error('Payment initiation error:', error)
        throw error
      }

      if (data?.success && data?.redirect_url) {
        console.log('Opening payment window:', data.redirect_url)
        // Open payment window
        window.open(data.redirect_url, '_blank', 'width=800,height=600')
        
        toast({
          title: 'Payment Initiated',
          description: 'Please complete your payment in the new window'
        })
      } else {
        console.error('Invalid payment response:', data)
        throw new Error('Failed to initiate payment - invalid response')
      }
    } catch (error) {
      console.error('Payment error:', error)
      toast({
        variant: 'destructive',
        title: 'Payment Error',
        description: 'Failed to initiate payment. Please try again.'
      })
      setHasStartedPayment(false)
    } finally {
      setLoading(false)
    }
  }

  return {
    loading,
    hasStartedPayment,
    initiatePayment,
    checkPaymentStatus
  }
}
