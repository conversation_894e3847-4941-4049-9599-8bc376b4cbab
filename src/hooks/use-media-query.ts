import { useState, useEffect } from 'react';

type MediaQueryCallback = (event: MediaQueryListEvent) => void;

/**
 * A custom React hook that tracks the state of a CSS media query.
 * @param {string} query - The media query to evaluate.
 * @returns {boolean} - Whether the media query matches.
 */
export function useMediaQuery(query: string): boolean {
  // Check if window is defined (for server-side rendering)
  const isClient = typeof window !== 'undefined';
  
  // Create a state variable to track if the media query matches
  const [matches, setMatches] = useState<boolean>(() => {
    if (!isClient) return false;
    return window.matchMedia(query).matches;
  });

  useEffect(() => {
    if (!isClient) return;

    // Get the media query list
    const mediaQuery = window.matchMedia(query);
    
    // Update state with current match value
    setMatches(mediaQuery.matches);
    
    // Create a callback function to handle changes
    const handleChange = (event: MediaQueryListEvent | { matches: boolean }) => {
      setMatches(event.matches);
    };
    
    // Add event listener for future changes
    // Use addListener for Safari < 14
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      mediaQuery.addListener(handleChange);
    }
    
    // Clean up the event listener
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        mediaQuery.removeListener(handleChange);
      }
    };
  }, [query, isClient]);

  return matches;
}

// Common media query presets

/**
 * Hook that returns true if the viewport width is 768px or less (mobile devices)
 */
export const useIsMobile = (): boolean => useMediaQuery('(max-width: 768px)');

/**
 * Hook that returns true if the viewport width is between 769px and 1024px (tablets)
 */
export const useIsTablet = (): boolean => 
  useMediaQuery('(min-width: 769px) and (max-width: 1024px)');

/**
 * Hook that returns true if the viewport width is 1025px or more (desktop)
 */
export const useIsDesktop = (): boolean => useMediaQuery('(min-width: 1025px)');
