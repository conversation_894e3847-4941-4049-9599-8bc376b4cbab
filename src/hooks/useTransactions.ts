
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { useDataRefresh } from '@/contexts/DataRefreshContext';
import type { Tables, TablesInsert } from '@/integrations/supabase/types';
import type { User } from '@supabase/supabase-js';

type Transaction = Tables<'transactions'>;
type TransactionInsert = TablesInsert<'transactions'>;

export const useTransactions = (user?: User | null) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { refreshTrigger } = useDataRefresh();

  const fetchTransactions = async () => {
    try {
      // Use the user prop instead of making auth calls
      if (!user) {
        setTransactions([]);
        setLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: false });

      if (error) throw error;
      setTransactions(data || []);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch transactions',
      });
    } finally {
      setLoading(false);
    }
  };

  const addTransaction = async (transaction: Omit<TransactionInsert, 'user_id'>) => {
    try {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('transactions')
        .insert([{ ...transaction, user_id: user.id }])
        .select()
        .single();

      if (error) throw error;
      setTransactions(prev => [data, ...prev]);
      toast({
        title: 'Success',
        description: 'Transaction added successfully',
      });
      return data;
    } catch (error) {
      console.error('Error adding transaction:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to add transaction',
      });
      throw error;
    }
  };

  useEffect(() => {
    if (user) {
      fetchTransactions();
    } else {
      // Clear transactions when user is not authenticated
      setTransactions([]);
      setLoading(false);
    }
  }, [user, refreshTrigger]);

  return {
    transactions,
    loading,
    addTransaction,
    refetch: fetchTransactions,
  };
};
