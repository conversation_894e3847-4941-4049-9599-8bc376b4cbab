
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

type Service = Tables<'services'>;
type ServiceInsert = TablesInsert<'services'>;
type ServiceUpdate = TablesUpdate<'services'>;

export const useServices = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchServices = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setServices([]);
        setLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setServices(data || []);
    } catch (error) {
      console.error('Error fetching services:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch services',
      });
    } finally {
      setLoading(false);
    }
  };

  const addService = async (service: Omit<ServiceInsert, 'user_id'>) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('services')
        .insert([{ ...service, user_id: user.id }])
        .select()
        .single();

      if (error) throw error;
      setServices(prev => [data, ...prev]);
      toast({
        title: 'Success',
        description: 'Service added successfully',
      });
      return data;
    } catch (error) {
      console.error('Error adding service:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to add service',
      });
      throw error;
    }
  };

  const updateService = async (id: string, updates: ServiceUpdate) => {
    try {
      const { data, error } = await supabase
        .from('services')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      setServices(prev => prev.map(s => s.id === id ? { ...s, ...data } : s));
      toast({
        title: 'Success',
        description: 'Service updated successfully',
      });
      return data;
    } catch (error) {
      console.error('Error updating service:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update service',
      });
      throw error;
    }
  };

  const deleteService = async (id: string) => {
    try {
      const { error } = await supabase
        .from('services')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setServices(prev => prev.filter(s => s.id !== id));
      toast({
        title: 'Success',
        description: 'Service deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting service:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete service',
      });
      throw error;
    }
  };

  useEffect(() => {
    fetchServices();
  }, []);

  return {
    services,
    loading,
    addService,
    updateService,
    deleteService,
    refetch: fetchServices,
  };
};
