import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { useToast } from '@/components/ui/use-toast';

interface InvoiceData {
  id: string;
  invoice_number: string;
  customer: string;
  due_date: string;
  amount: number;
  status: string;
  items?: any[];
  notes?: string;
  created_at: string;
}

interface CompanySettings {
  company_name?: string;
  avatar_url?: string;
  display_name?: string;
  email?: string;
  currency?: string;
  country?: string;
}

export const useInvoicePrint = () => {
  const { toast } = useToast();
  const componentRef = useRef<HTMLDivElement>(null);

  // Print functionality
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: (invoice: InvoiceData) => `Invoice-${invoice.invoice_number}`,
    onBeforeGetContent: () => {
      // Add any pre-print setup here
      return Promise.resolve();
    },
    onAfterPrint: () => {
      toast({
        title: 'Success',
        description: 'Invoice sent to printer successfully',
      });
    },
    onPrintError: (error) => {
      console.error('Print error:', error);
      toast({
        variant: 'destructive',
        title: 'Print Error',
        description: 'Failed to print invoice. Please try again.',
      });
    },
  });

  // PDF download functionality
  const handleDownloadPDF = async (invoice: InvoiceData, companySettings: CompanySettings) => {
    try {
      if (!componentRef.current) {
        throw new Error('Invoice template not found');
      }

      toast({
        title: 'Generating PDF',
        description: 'Please wait while we generate your PDF...',
      });

      // Create canvas from the invoice template
      const canvas = await html2canvas(componentRef.current, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
      });

      // Calculate dimensions
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const imgWidth = canvas.width;
      const imgHeight = canvas.height;
      
      // Calculate scaling to fit the page
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
      const scaledWidth = imgWidth * ratio;
      const scaledHeight = imgHeight * ratio;
      
      // Center the image on the page
      const x = (pdfWidth - scaledWidth) / 2;
      const y = (pdfHeight - scaledHeight) / 2;

      // Add the image to PDF
      pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

      // Save the PDF
      const fileName = `Invoice-${invoice.invoice_number}.pdf`;
      pdf.save(fileName);

      toast({
        title: 'Success',
        description: `PDF downloaded as ${fileName}`,
      });
    } catch (error) {
      console.error('PDF generation error:', error);
      toast({
        variant: 'destructive',
        title: 'PDF Generation Error',
        description: 'Failed to generate PDF. Please try again.',
      });
    }
  };

  // Validate company settings
  const validateCompanySettings = (settings: CompanySettings): { isValid: boolean; missingFields: string[] } => {
    const missingFields: string[] = [];
    
    if (!settings.company_name && !settings.display_name) {
      missingFields.push('Company Name');
    }
    
    if (!settings.email) {
      missingFields.push('Email');
    }

    return {
      isValid: missingFields.length === 0,
      missingFields
    };
  };

  // Show settings warning if company info is incomplete
  const showSettingsWarning = (missingFields: string[]) => {
    toast({
      variant: 'destructive',
      title: 'Incomplete Company Settings',
      description: `Please configure the following in Settings: ${missingFields.join(', ')}`,
    });
  };

  return {
    componentRef,
    handlePrint,
    handleDownloadPDF,
    validateCompanySettings,
    showSettingsWarning,
  };
};
