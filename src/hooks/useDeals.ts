
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

type Deal = Tables<'deals'>;
type DealInsert = TablesInsert<'deals'>;
type DealUpdate = TablesUpdate<'deals'>;

export const useDeals = () => {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchDeals = async () => {
    try {
      const { data, error } = await supabase
        .from('deals')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setDeals(data || []);
    } catch (error) {
      console.error('Error fetching deals:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch deals',
      });
    } finally {
      setLoading(false);
    }
  };

  const addDeal = async (deal: Omit<DealInsert, 'user_id'>) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('deals')
        .insert([{ ...deal, user_id: user.id }])
        .select()
        .single();

      if (error) throw error;
      setDeals(prev => [data, ...prev]);
      toast({
        title: 'Success',
        description: 'Deal added successfully',
      });
      return data;
    } catch (error) {
      console.error('Error adding deal:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to add deal',
      });
      throw error;
    }
  };

  const deleteDeal = async (id: string) => {
    try {
      const { error } = await supabase
        .from('deals')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setDeals(prev => prev.filter(d => d.id !== id));
      toast({
        title: 'Success',
        description: 'Deal deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting deal:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete deal',
      });
      throw error;
    }
  };

  useEffect(() => {
    fetchDeals();
  }, []);

  return {
    deals,
    loading,
    addDeal,
    deleteDeal,
    refetch: fetchDeals,
  };
};
