
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

type Quote = Tables<'quotes'>;
type QuoteInsert = TablesInsert<'quotes'>;
type QuoteUpdate = TablesUpdate<'quotes'>;

export const useQuotes = () => {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchQuotes = async () => {
    try {
      const { data, error } = await supabase
        .from('quotes')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setQuotes(data || []);
    } catch (error) {
      console.error('Error fetching quotes:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch quotes',
      });
    } finally {
      setLoading(false);
    }
  };

  const addQuote = async (quote: Omit<QuoteInsert, 'user_id'>) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('quotes')
        .insert([{ ...quote, user_id: user.id }])
        .select()
        .single();

      if (error) throw error;
      setQuotes(prev => [data, ...prev]);
      toast({
        title: 'Success',
        description: 'Quote added successfully',
      });
      return data;
    } catch (error) {
      console.error('Error adding quote:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to add quote',
      });
      throw error;
    }
  };

  const deleteQuote = async (id: string) => {
    try {
      const { error } = await supabase
        .from('quotes')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setQuotes(prev => prev.filter(q => q.id !== id));
      toast({
        title: 'Success',
        description: 'Quote deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting quote:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete quote',
      });
      throw error;
    }
  };

  useEffect(() => {
    fetchQuotes();
  }, []);

  return {
    quotes,
    loading,
    addQuote,
    deleteQuote,
    refetch: fetchQuotes,
  };
};
