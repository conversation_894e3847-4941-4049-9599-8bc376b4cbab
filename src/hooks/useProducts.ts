import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { useDataRefresh } from '@/contexts/DataRefreshContext';
import type { User } from '@supabase/supabase-js';

// Simple product type
interface Product {
  id: string;
  name: string;
  sku: string;
  category: string;
  price: number;
  stock: number;
  status: string;
  user_id: string;
  created_at: string;
}

interface ProductInput {
  name: string;
  sku?: string;
  category: string;
  price: number;
  stock: number;
  status: string;
}

export function useProducts(user: User | null) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { refreshTrigger } = useDataRefresh();

  // Generate simple SKU
  const generateSKU = () => {
    return `PRD-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
  };

  // Fetch products
  const fetchProducts = async () => {
    if (!user) {
      setProducts([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching products:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load products',
        });
        return;
      }

      setProducts(data || []);
    } catch (error: any) {
      console.error('Unexpected error:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'An unexpected error occurred',
      });
    } finally {
      setLoading(false);
    }
  };

  // Add product
  const addProduct = async (productInput: ProductInput) => {
    if (!user) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'You must be logged in to add products',
      });
      return;
    }

    try {
      const productData = {
        ...productInput,
        sku: productInput.sku || generateSKU(),
        user_id: user.id,
      };

      const { data, error } = await supabase
        .from('products')
        .insert([productData])
        .select()
        .single();

      if (error) {
        console.error('Error adding product:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to add product',
        });
        return;
      }

      if (!data) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Product was not created properly',
        });
        return;
      }

      // Update products list immediately
      setProducts(prev => [data, ...prev]);

      toast({
        title: 'Success',
        description: `Product "${data.name}" added successfully`,
      });

      return data;
    } catch (error: any) {
      console.error('Error adding product:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: `Failed to add product: ${error.message || 'Unknown error'}`,
      });
    }
  };

  // Update product
  const updateProduct = async (id: string, updates: Partial<ProductInput>) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('products')
        .update(updates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating product:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to update product',
        });
        return;
      }

      if (!data) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Product not found or you do not have permission to update it',
        });
        return;
      }

      // Update the product in the local state
      setProducts(prev => prev.map(p => p.id === id ? data : p));

      return data;
    } catch (error) {
      console.error('Error updating product:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update product',
      });
    }
  };

  // Delete product
  const deleteProduct = async (id: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting product:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to delete product',
        });
        return;
      }

      setProducts(prev => prev.filter(p => p.id !== id));
      toast({
        title: 'Success',
        description: 'Product deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete product',
      });
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [user, refreshTrigger]);

  return {
    products,
    loading,
    addProduct,
    updateProduct,
    deleteProduct,
    refetch: fetchProducts,
  };
}
