import { useState, useEffect, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { useDataRefresh } from '@/contexts/DataRefreshContext';
import type { User } from '@supabase/supabase-js';

interface RevenueData {
  totalRevenue: number;
  receiptRevenue: number;
  transactionRevenue: number;
  serviceRevenue: number;
  totalSales: number;
  receiptSales: number;
  transactionSales: number;
  monthlyData: Array<{
    month: string;
    receipts: number;
    transactions: number;
    services: number;
    total: number;
  }>;
  revenueBreakdown: Array<{
    name: string;
    value: number;
    color: string;
  }>;
}

export const useRevenue = (user?: User | null) => {
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { refreshTrigger } = useDataRefresh();

  const [receipts, setReceipts] = useState<any[]>([]);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [services, setServices] = useState<any[]>([]);

  const fetchAllRevenueData = async () => {
    try {
      if (!user) {
        setReceipts([]);
        setTransactions([]);
        setServices([]);
        setLoading(false);
        return;
      }

      // Fetch receipts (paid only for revenue)
      const { data: receiptsData, error: receiptsError } = await supabase
        .from('invoices')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'paid')
        .order('created_at', { ascending: false });

      if (receiptsError) throw receiptsError;

      // Fetch transactions (sales only)
      const { data: transactionsData, error: transactionsError } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', user.id)
        .eq('type', 'sale')
        .order('date', { ascending: false });

      if (transactionsError) throw transactionsError;

      // Fetch services for total revenue tracking
      const { data: servicesData, error: servicesError } = await supabase
        .from('services')
        .select('*')
        .eq('user_id', user.id);

      if (servicesError) throw servicesError;

      setReceipts(receiptsData || []);
      setTransactions(transactionsData || []);
      setServices(servicesData || []);
    } catch (error) {
      console.error('Error fetching revenue data:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch revenue data',
      });
    } finally {
      setLoading(false);
    }
  };

  const revenueData: RevenueData = useMemo(() => {
    // Calculate receipt revenue
    const receiptRevenue = receipts.reduce((sum, receipt) => sum + Number(receipt.amount), 0);
    const receiptSales = receipts.length;

    // Calculate transaction revenue
    const transactionRevenue = transactions.reduce((sum, transaction) => sum + Number(transaction.amount), 0);
    const transactionSales = transactions.length;

    // Calculate service revenue from services table
    const serviceRevenue = services.reduce((sum, service) => sum + Number(service.total_revenue || 0), 0);

    // Total revenue and sales
    const totalRevenue = receiptRevenue + transactionRevenue;
    const totalSales = receiptSales + transactionSales;

    // Calculate monthly data
    const monthlyData = (() => {
      const monthMap = new Map<string, { receipts: number; transactions: number; services: number }>();

      // Process receipts
      receipts.forEach(receipt => {
        const month = new Date(receipt.created_at).toLocaleDateString('en-US', { month: 'short' });
        const existing = monthMap.get(month) || { receipts: 0, transactions: 0, services: 0 };
        existing.receipts += Number(receipt.amount);
        monthMap.set(month, existing);
      });

      // Process transactions
      transactions.forEach(transaction => {
        const month = new Date(transaction.date).toLocaleDateString('en-US', { month: 'short' });
        const existing = monthMap.get(month) || { receipts: 0, transactions: 0, services: 0 };
        existing.transactions += Number(transaction.amount);
        monthMap.set(month, existing);
      });

      // Convert to array and add totals
      return Array.from(monthMap.entries()).map(([month, data]) => ({
        month,
        receipts: data.receipts,
        transactions: data.transactions,
        services: data.services,
        total: data.receipts + data.transactions + data.services,
      }));
    })();

    // Revenue breakdown for charts
    const revenueBreakdown = [
      {
        name: 'Receipts',
        value: receiptRevenue,
        color: '#0088FE',
      },
      {
        name: 'POS Sales',
        value: transactionRevenue,
        color: '#00C49F',
      },
      {
        name: 'Services',
        value: serviceRevenue,
        color: '#FFBB28',
      },
    ].filter(item => item.value > 0);

    return {
      totalRevenue,
      receiptRevenue,
      transactionRevenue,
      serviceRevenue,
      totalSales,
      receiptSales,
      transactionSales,
      monthlyData,
      revenueBreakdown,
    };
  }, [receipts, transactions, services]);

  useEffect(() => {
    fetchAllRevenueData();
  }, [user, refreshTrigger]);

  return {
    revenueData,
    loading,
    refetch: fetchAllRevenueData,
  };
};
