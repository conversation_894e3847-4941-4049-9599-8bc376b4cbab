import { useState, useEffect } from 'react';

const TARGET_EMAIL = '<EMAIL>';
const POPUP_DELAY = 180000; // 3 minutes in milliseconds

export const usePackagesPopup = (userEmail: string | undefined) => {
  const [shouldShowPopup, setShouldShowPopup] = useState(false);
  const [popupTimer, setPopupTimer] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Clear any existing timer
    if (popupTimer) {
      clearTimeout(popupTimer);
      setPopupTimer(null);
    }

    // Only show popup for the target email
    if (!userEmail || userEmail !== TARGET_EMAIL) {
      setShouldShowPopup(false);
      return;
    }

    console.log(`Setting popup timer for ${TARGET_EMAIL} - will show in 3 minutes`);

    // Set timer to show popup after 3 minutes
    const timer = setTimeout(() => {
      console.log('3 minute timer fired, showing packages popup');
      setShouldShowPopup(true);
    }, POPUP_DELAY);

    setPopupTimer(timer);

    // Cleanup function
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [userEmail]);

  const dismissPopup = () => {
    console.log('Packages popup dismissed, setting timer for next appearance');
    
    // Hide popup immediately
    setShouldShowPopup(false);

    // Set timer for next popup (3 minutes)
    const timer = setTimeout(() => {
      console.log('3 minute timer after dismissal fired, showing popup again');
      setShouldShowPopup(true);
    }, POPUP_DELAY);
    
    setPopupTimer(timer);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (popupTimer) {
        clearTimeout(popupTimer);
      }
    };
  }, [popupTimer]);

  return {
    shouldShowPopup,
    dismissPopup,
    isTargetUser: userEmail === TARGET_EMAIL
  };
};
