
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

type Company = Tables<'companies'>;
type CompanyInsert = TablesInsert<'companies'>;
type CompanyUpdate = TablesUpdate<'companies'>;

export const useCompanies = () => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchCompanies = async () => {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setCompanies(data || []);
    } catch (error) {
      console.error('Error fetching companies:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch companies',
      });
    } finally {
      setLoading(false);
    }
  };

  const addCompany = async (company: Omit<CompanyInsert, 'user_id'>) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('companies')
        .insert([{ ...company, user_id: user.id }])
        .select()
        .single();

      if (error) throw error;
      setCompanies(prev => [data, ...prev]);
      toast({
        title: 'Success',
        description: 'Company added successfully',
      });
      return data;
    } catch (error) {
      console.error('Error adding company:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to add company',
      });
      throw error;
    }
  };

  const deleteCompany = async (id: string) => {
    try {
      const { error } = await supabase
        .from('companies')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setCompanies(prev => prev.filter(c => c.id !== id));
      toast({
        title: 'Success',
        description: 'Company deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting company:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete company',
      });
      throw error;
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, []);

  return {
    companies,
    loading,
    addCompany,
    deleteCompany,
    refetch: fetchCompanies,
  };
};
