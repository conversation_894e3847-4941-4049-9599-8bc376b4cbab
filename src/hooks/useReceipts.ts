import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { useDataRefresh } from '@/contexts/DataRefreshContext';
import type { User } from '@supabase/supabase-js';

interface Receipt {
  id: string;
  receipt_number?: string;
  invoice_number?: string; // For backward compatibility
  customer: string;
  due_date: string;
  amount: number;
  status: string;
  items?: any[];
  notes?: string;
  created_at: string;
  user_id: string;
}

export const useReceipts = () => {
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { triggerRefresh } = useDataRefresh();

  const fetchReceipts = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setReceipts([]);
        setLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('invoices') // Using the same table as invoices for now
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase error fetching receipts:', error);
        throw error;
      }

      setReceipts(data || []);
    } catch (error) {
      console.error('Error fetching receipts:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch receipts',
      });
    } finally {
      setLoading(false);
    }
  };

  const updateInventoryAndRevenue = async (items: any[], isPaid: boolean) => {
    if (!isPaid || !items || items.length === 0) return;

    try {
      // Update product stock levels
      for (const item of items) {
        if (item.type === 'product' && item.product_id) {
          // First get current stock
          const { data: currentProduct, error: fetchError } = await supabase
            .from('products')
            .select('stock')
            .eq('id', item.product_id)
            .single();

          if (fetchError || !currentProduct) {
            console.error('Error fetching current product stock:', fetchError);
            throw new Error(`Failed to fetch stock for ${item.name}`);
          }

          const newStock = currentProduct.stock - item.quantity;

          if (newStock < 0) {
            throw new Error(`Insufficient stock for ${item.name}. Available: ${currentProduct.stock}, Required: ${item.quantity}`);
          }

          // Update stock
          const { error: stockError } = await supabase
            .from('products')
            .update({
              stock: newStock,
              updated_at: new Date().toISOString()
            })
            .eq('id', item.product_id);

          if (stockError) {
            console.error('Error updating product stock:', stockError);
            throw new Error(`Failed to update stock for ${item.name}`);
          }

          console.log(`Updated stock for ${item.name}: ${currentProduct.stock} -> ${newStock}`);
        }

        // Update service revenue and sales count
        if (item.type === 'service' && item.service_id) {
          // First get current values
          const { data: currentService, error: fetchError } = await supabase
            .from('services')
            .select('total_sales, total_revenue')
            .eq('id', item.service_id)
            .single();

          if (fetchError || !currentService) {
            console.error('Error fetching current service data:', fetchError);
            throw new Error(`Failed to fetch service data for ${item.name}`);
          }

          const newSales = (currentService.total_sales || 0) + item.quantity;
          const newRevenue = (currentService.total_revenue || 0) + item.amount;

          const { error: serviceError } = await supabase
            .from('services')
            .update({
              total_sales: newSales,
              total_revenue: newRevenue,
              updated_at: new Date().toISOString()
            })
            .eq('id', item.service_id);

          if (serviceError) {
            console.error('Error updating service revenue:', serviceError);
            throw new Error(`Failed to update revenue for ${item.name}`);
          }

          console.log(`Updated service ${item.name}: sales ${currentService.total_sales} -> ${newSales}, revenue ${currentService.total_revenue} -> ${newRevenue}`);
        }
      }
    } catch (error) {
      console.error('Error updating inventory and revenue:', error);
      throw error;
    }
  };

  const addReceipt = async (receiptData: Omit<Receipt, 'id' | 'created_at' | 'user_id'>) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');



      // Check stock availability for products if receipt is paid
      if (receiptData.status === 'paid' && receiptData.items) {
        for (const item of receiptData.items) {
          if (item.type === 'product' && item.product_id) {
            const { data: product, error } = await supabase
              .from('products')
              .select('stock, name')
              .eq('id', item.product_id)
              .single();

            if (error || !product) {
              throw new Error(`Product not found: ${item.name}`);
            }

            if (product.stock < item.quantity) {
              throw new Error(`Insufficient stock for ${product.name}. Available: ${product.stock}, Required: ${item.quantity}`);
            }
          }
        }
      }

      const insertData = {
        customer: receiptData.customer,
        due_date: receiptData.due_date,
        amount: receiptData.amount,
        status: receiptData.status,
        items: receiptData.items,
        notes: receiptData.notes,
        user_id: user.id,
        // Use receipt_number if provided, otherwise use invoice_number for backward compatibility
        invoice_number: receiptData.receipt_number || receiptData.invoice_number || `RCP-${Date.now()}`,
      };



      const { data, error } = await supabase
        .from('invoices') // Using the same table as invoices for now
        .insert([insertData])
        .select()
        .single();

      if (error) {
        console.error('Supabase insert error:', error);
        throw error;
      }



      // Update inventory and revenue if receipt is paid
      await updateInventoryAndRevenue(receiptData.items, receiptData.status === 'paid');

      setReceipts(prev => [data, ...prev]);

      // Trigger refresh for other components (like inventory)
      triggerRefresh();

      const successMessage = receiptData.status === 'paid'
        ? 'Receipt created and inventory/revenue updated successfully'
        : 'Receipt created successfully';

      toast({
        title: 'Success',
        description: successMessage,
      });

      return data;
    } catch (error) {
      console.error('Error creating receipt:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create receipt',
      });
      throw error;
    }
  };

  const updateReceiptStatus = async (receiptId: string, newStatus: string, receiptItems?: any[]) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get current receipt to check previous status
      const { data: currentReceipt, error: fetchError } = await supabase
        .from('invoices')
        .select('*')
        .eq('id', receiptId)
        .single();

      if (fetchError) throw fetchError;

      // Update receipt status
      const { data, error } = await supabase
        .from('invoices')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', receiptId)
        .select()
        .single();

      if (error) throw error;

      // If changing to paid status, update inventory and revenue
      if (newStatus === 'paid' && currentReceipt.status !== 'paid') {
        const items = receiptItems || currentReceipt.items;
        await updateInventoryAndRevenue(items, true);
      }

      setReceipts(prev => prev.map(receipt =>
        receipt.id === receiptId ? data : receipt
      ));

      toast({
        title: 'Success',
        description: `Receipt status updated to ${newStatus}`,
      });

      return data;
    } catch (error) {
      console.error('Error updating receipt status:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update receipt status',
      });
      throw error;
    }
  };

  const deleteReceipt = async (receiptId: string) => {
    try {
      const { error } = await supabase
        .from('invoices') // Using the same table as invoices for now
        .delete()
        .eq('id', receiptId);

      if (error) throw error;

      setReceipts(prev => prev.filter(receipt => receipt.id !== receiptId));
      toast({
        title: 'Success',
        description: 'Receipt deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting receipt:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete receipt',
      });
      throw error;
    }
  };

  useEffect(() => {
    fetchReceipts();
  }, []);

  return {
    receipts,
    loading,
    addReceipt,
    updateReceiptStatus,
    deleteReceipt,
    refetch: fetchReceipts,
  };
};
