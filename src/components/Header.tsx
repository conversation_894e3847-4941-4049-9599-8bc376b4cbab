
import { useState, useEffect } from "react";
import { MenuIcon, User, LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

import { MobileSidebar } from "@/components/MobileSidebar";
import { NotificationDropdown } from "@/components/NotificationDropdown";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useNavigate, Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { performLogout } from "@/utils/logout";
import { useUserSettings } from "@/hooks/useUserSettings";
import type { User as SupabaseUser } from "@supabase/supabase-js";

interface HeaderProps {
  onMenuClick: () => void;
  isMobileMenuOpen: boolean;
  isSidebarCollapsed: boolean;
  user?: SupabaseUser | null;
}

export function Header({ onMenuClick, isMobileMenuOpen, isSidebarCollapsed, user }: HeaderProps) {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { settings } = useUserSettings(user);

  // Derive user profile from settings - this will automatically update when settings change
  const userProfile = settings ? {
    display_name: settings.display_name || 'User',
    avatar_url: settings.avatar_url || ''
  } : null;


  
  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const handleProfileClick = () => {
    navigate('/profile');
  };

  const handleLogout = async () => {
    await performLogout({
      navigate,
      showToast: toast,
      source: 'header'
    });
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase() || 'U';
  };

  return (
    <>
      <MobileSidebar 
        isOpen={isMobileSidebarOpen} 
        onClose={() => setIsMobileSidebarOpen(false)} 
      />
      
      <header className="fixed top-0 left-0 right-0 z-50 h-16 bg-gradient-to-r from-blue-600 to-blue-700 backdrop-blur-md border-b border-blue-500/30 shadow-lg">
        <div className="flex items-center justify-between h-full px-4 lg:px-6">
          {/* Left side - Menu and Logo */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMobileSidebar}
              className="lg:hidden h-9 w-9 text-white hover:bg-white/10 transition-colors"
              aria-label="Toggle menu"
            >
              <MenuIcon className="h-5 w-5" />
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuClick}
              className="hidden lg:flex h-9 w-9 text-white hover:bg-white/10 transition-colors"
              aria-label="Toggle sidebar"
            >
              <MenuIcon className="h-5 w-5" />
            </Button>
            

          </div>

          {/* Right side - Notifications and Profile */}
          <div className="flex items-center gap-2">
            {/* Notifications */}
            <NotificationDropdown user={user} />

            {/* Profile Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center gap-2 ml-2 text-white hover:bg-white/10 transition-colors">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={userProfile?.avatar_url} alt="Logo" />
                    <AvatarFallback className="bg-white/20 text-white text-sm font-medium border border-white/30">
                      {getInitials(userProfile?.display_name || 'User')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="hidden lg:block text-left">
                    <p className="text-sm font-medium text-white">
                      {userProfile?.display_name || 'User'}
                    </p>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem onClick={handleProfileClick}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>
    </>
  );
}
