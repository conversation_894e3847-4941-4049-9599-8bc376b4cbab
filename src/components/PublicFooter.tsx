import { Link } from "react-router-dom";

export function PublicFooter() {
  return (
    <footer className="bg-gray-900 text-white py-12 mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center gap-2 mb-4">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                <span className="text-white font-bold text-sm">vQ</span>
              </div>
              <span className="font-bold text-lg">vertiQ</span>
            </div>
            <p className="text-gray-400">
              Modern business management solution designed for the digital age.
            </p>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/" className="hover:text-white transition-colors">Home</Link></li>
              <li><Link to="/about" className="hover:text-white transition-colors">About Us</Link></li>
              <li><Link to="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
              <li><Link to="/contact" className="hover:text-white transition-colors">Contact</Link></li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Contact Info</h4>
            <ul className="space-y-2 text-gray-400">
              <li><EMAIL></li>
              <li>+254 110860589</li>
              <li>24/7 Support Available</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Get in Touch</h4>
            <div className="flex space-x-4">
              <a 
                href="https://wa.me/254110860589" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-gray-400 hover:text-white transition-colors"
              >
                WhatsApp
              </a>
              <a 
                href="tel:+254110860589" 
                className="text-gray-400 hover:text-white transition-colors"
              >
                Call Us
              </a>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; {new Date().getFullYear()} vertiQ. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
