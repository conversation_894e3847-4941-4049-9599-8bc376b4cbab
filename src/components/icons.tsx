import { SVGProps } from 'react';

type IconProps = SVGProps<SVGSVGElement>;

export function Spinner(props: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M21 12a9 9 0 1 1-6.219-8.56" />
    </svg>
  );
}

export function Google(props: IconProps) {
  return (
    <svg role="img" viewBox="0 0 24 24" {...props}>
      <path
        fill="currentColor"
        d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.293-3.36 2.027-2.187 2.027-5.253 2.027-6.64 0-.52-.053-1.04-.16-1.56H12.48z"
      />
    </svg>
  );
}

export function Microsoft(props: IconProps) {
  return (
    <svg role="img" viewBox="0 0 24 24" {...props}>
      <path
        fill="currentColor"
        d="M11.5 3v8.5H3V3h8.5zm1-1H2v10.5h10.5V2zM12 13v8.5H21V13h-9zm-1 1h10.5v6.5H11V14z"
      />
    </svg>
  );
}

export const Icons = {
  spinner: Spinner,
  google: Google,
  microsoft: Microsoft,
};
