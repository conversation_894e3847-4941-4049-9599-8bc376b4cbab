import { useState, useMemo } from "react";
import { useLocation, Link, useNavigate } from "react-router-dom";
import {
  Home,
  Users,
  Building,
  ListChecks,
  ShoppingCart,
  BarChart3,
  FileText,
  Settings,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  X,
  LogOut,
  User
} from "lucide-react";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { performLogout } from "@/utils/logout";

// Type definitions
interface MenuItemBase {
  title: string;
  url?: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string | number;
}

interface SubMenuItem {
  title: string;
  url: string;
  badge?: string | number;
}

interface MenuItemWithChildren extends MenuItemBase {
  items: SubMenuItem[];
}

type MenuItem = MenuItemBase | MenuItemWithChildren;

// Menu items - matching AppSidebar exactly
const items: Array<MenuItemBase | MenuItemWithChildren> = [
  {
    title: 'Dashboard',
    icon: Home,
    url: '/dashboard'
  },
  {
    title: 'Inventory',
    icon: Building,
    url: '/inventory'
  },
  {
    title: 'Services',
    icon: ListChecks,
    url: '/services'
  },
  {
    title: 'POS',
    icon: ShoppingCart,
    url: '/pos'
  },
  {
    title: 'Sales',
    icon: ShoppingCart,
    url: '/sales'
  },
  {
    title: 'Expenses',
    icon: BarChart3,
    url: '/expenses'
  },
  {
    title: 'Receipts',
    icon: FileText,
    url: '/receipts'
  },
  {
    title: 'Quotes',
    icon: FileText,
    url: '/quotes'
  },
  {
    title: 'CRM',
    icon: Users,
    items: [
      { title: 'Contacts', url: '/crm/contacts' },
      { title: 'Companies', url: '/crm/companies' },
      { title: 'Deals', url: '/crm/deals' },
      { title: 'Tasks', url: '/crm/tasks' }
    ]
  },
  {
    title: 'Profile',
    icon: User,
    url: '/profile'
  },
  {
    title: 'Settings',
    icon: Settings,
    url: '/settings'
  }
];

interface MobileSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileSidebar({ isOpen, onClose }: MobileSidebarProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [openItems, setOpenItems] = useState<Record<string, boolean>>({
    'Inventory': true,
    'Services': true,
    'Sales': true,
    'Expenses': true,
    'CRM': true
  });
  
  const filteredItems = useMemo(() => {
    if (!searchQuery.trim()) return items;
    
    const query = searchQuery.toLowerCase().trim();
    return items.filter(item => {
      const matchesTitle = item.title.toLowerCase().includes(query);
      const matchesChildren = 'items' in item 
        ? item.items.some(subItem => subItem.title.toLowerCase().includes(query))
        : false;
      return matchesTitle || matchesChildren;
    });
  }, [searchQuery]);

  const toggleSection = (title: string) => {
    setOpenItems(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  const isActive = (path: string | undefined) => {
    if (!path) return false;
    return location.pathname === path ||
           (path !== '/' && location.pathname.startsWith(`${path}/`));
  };

  const handleLogout = async () => {
    await performLogout({
      navigate,
      showToast: toast,
      onSuccess: onClose, // Close mobile sidebar on successful logout
      source: 'mobile-sidebar'
    });
  };

  const renderItem = (item: MenuItem) => {
    const isItemActive = item.url ? isActive(item.url) : false;
    const Icon = item.icon;

    if ('items' in item) {
      const hasActiveChild = item.items.some((subItem) => isActive(subItem.url));
      const isOpen = openItems[item.title] ?? true;
      
      return (
        <div key={item.title} className="space-y-1">
          <button
            onClick={() => toggleSection(item.title)}
            className={cn(
              "w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors",
              (isItemActive || hasActiveChild) 
                ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-100" 
                : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
            )}
          >
            <div className={cn(
              "p-1.5 rounded-md mr-3",
              (isItemActive || hasActiveChild) 
                ? "bg-blue-100 text-blue-600 dark:bg-blue-800/50" 
                : "bg-gray-100 text-gray-600 dark:bg-gray-800"
            )}>
              <Icon className="h-5 w-5" />
            </div>
            <span className="text-left">{item.title}</span>
            <ChevronDown className={cn(
              "ml-auto h-4 w-4 transition-transform duration-200 text-gray-400",
              isOpen ? "rotate-0" : "-rotate-90"
            )} />
          </button>
          
          {isOpen && (
            <div className="pl-14 space-y-1">
              {item.items.map((subItem) => {
                const isSubItemActive = isActive(subItem.url);
                return (
                  <Link
                    key={subItem.title}
                    to={subItem.url}
                    onClick={onClose}
                    className={cn(
                      "block px-4 py-2.5 text-sm rounded-lg mx-2 transition-colors",
                      isSubItemActive
                        ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-100"
                        : "text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800"
                    )}
                  >
                    {subItem.title}
                  </Link>
                );
              })}
            </div>
          )}
        </div>
      );
    }

    return (
      <Link
        to={item.url || '#'}
        onClick={onClose}
        className={cn(
          "flex items-center px-4 py-3 text-sm font-medium rounded-lg mx-1 transition-colors",
          isItemActive
            ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-100"
            : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
        )}
      >
        <div className={cn(
          "p-1.5 rounded-md mr-3",
          isItemActive 
            ? "bg-blue-100 text-blue-600 dark:bg-blue-800/50" 
            : "bg-gray-100 text-gray-600 dark:bg-gray-800"
        )}>
          <Icon className="h-5 w-5" />
        </div>
        <span className="text-left">{item.title}</span>
      </Link>
    );
  };

  return (
    <>
      {/* Overlay */}
      <div 
        className={cn(
          "fixed inset-0 bg-black/50 z-50 transition-opacity",
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Sidebar */}
      <div 
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-900 shadow-xl transform transition-transform duration-300 ease-in-out flex flex-col h-full",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-800">
            <div className="text-xl font-semibold text-gray-900 dark:text-white">
              Menu
            </div>
            <button
              onClick={onClose}
              className="p-1 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          {/* Search */}
          <div className="p-3">
            <div className="relative">
              <Input
                type="search"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 bg-gray-100 dark:bg-gray-800 border-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:ring-1 focus-visible:ring-gray-200 dark:focus-visible:ring-gray-700 focus-visible:ring-offset-0 h-9"
              />
            </div>
          </div>
          
          {/* Navigation */}
          <ScrollArea className="flex-1">
            <nav className="p-2 space-y-1">
              {filteredItems.map((item) => (
                <div key={item.title} className="mb-1">
                  {renderItem(item)}
                </div>
              ))}
            </nav>
          </ScrollArea>
          
          {/* Footer */}
          <div className="p-3 border-t border-gray-200 dark:border-gray-800">
            <Button
              variant="ghost"
              className="w-full justify-start text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={handleLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign out
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
