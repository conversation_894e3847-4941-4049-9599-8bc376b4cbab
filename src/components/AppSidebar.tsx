import React, { useState, useEffect, useMemo } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sidebar, SidebarContent, SidebarMenu, SidebarMenuItem, SidebarMenuSub, SidebarMenuSubItem } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { Home, LayoutDashboard, Users, Building, ShoppingCart, ListChecks, BarChart, Settings, ChevronDown, FileText, User } from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button";
import { SidebarMenuButton } from './ui/sidebar';

interface MenuItemBase {
  title: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  url?: string;
}

interface MenuItemWithChildren extends MenuItemBase {
  items: SubMenuItem[];
}

interface SubMenuItem {
  title: string;
  url: string;
}

interface AppSidebarProps {
  isCollapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  onNavigate?: () => void;
}

export const AppSidebar = ({ isCollapsed = false, onCollapse, onNavigate }: AppSidebarProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [openItems, setOpenItems] = useState<{ [key: string]: boolean }>({});

  const toggleItem = (title: string) => {
    setOpenItems(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  const isActive = (url: string) => {
    return location.pathname === url;
  };

  const menuItems: (MenuItemBase | MenuItemWithChildren)[] = useMemo(() => [
    { title: 'Dashboard', icon: LayoutDashboard, url: '/dashboard' },
    {
      title: 'Inventory',
      icon: Building,
      url: '/inventory'
    },
    {
      title: 'Services',
      icon: ListChecks,
      url: '/services'
    },
    {
      title: 'POS',
      icon: ShoppingCart,
      url: '/pos'
    },
    {
      title: 'Sales',
      icon: ShoppingCart,
      url: '/sales'
    },
    {
      title: 'Expenses',
      icon: BarChart,
      url: '/expenses'
    },
    { title: 'Receipts', icon: FileText, url: '/receipts' },
    { title: 'Quotes', icon: FileText, url: '/quotes' },
    {
      title: 'CRM',
      icon: Users,
      items: [
        { title: 'Contacts', url: '/crm/contacts' },
        { title: 'Companies', url: '/crm/companies' },
        { title: 'Deals', url: '/crm/deals' },
        { title: 'Tasks', url: '/crm/tasks' },
      ],
    },
    { title: 'Profile', icon: User, url: '/profile' },
    { title: 'Settings', icon: Settings, url: '/settings' },
  ], []);

  const filteredItems = useMemo(() => {
    return menuItems;
  }, [menuItems]);

  const renderItem = (item: MenuItemBase | MenuItemWithChildren) => {
    const isItemActive = item.url ? isActive(item.url) : false;
    const Icon = item.icon;

    if ('items' in item) {
      const hasActiveChild = item.items.some((subItem) => isActive(subItem.url));
      
      return (
        <Collapsible 
          key={item.title} 
          defaultOpen={!isCollapsed || hasActiveChild}
          className="space-y-1"
        >
          <SidebarMenuButton 
            asChild
            className={cn(
              "w-full transition-all duration-200 hover:bg-white/10",
              (isItemActive || hasActiveChild) 
                ? "bg-white/20 text-white" 
                : "text-white/90 hover:text-white"
            )}
          >
            <CollapsibleTrigger className="w-full group">
              <div className={cn(
                "p-1 rounded-md transition-colors",
                (isItemActive || hasActiveChild) 
                  ? "bg-white/30 text-white" 
                  : "text-white/70 group-hover:bg-white/10"
              )}>
                <Icon className="h-5 w-5 flex-shrink-0" />
              </div>
              {!isCollapsed && (
                <>
                  <span className="ml-3 text-sm font-medium">{item.title}</span>
                  <ChevronDown className={cn(
                    "ml-auto h-4 w-4 transition-transform duration-200",
                    openItems[item.title] ? "rotate-0" : "-rotate-90"
                  )} />
                </>
              )}
            </CollapsibleTrigger>
          </SidebarMenuButton>
          {!isCollapsed && (
            <CollapsibleContent className="overflow-hidden data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up">
              <SidebarMenuSub className="pl-2 mt-1 space-y-1 border-l-2 border-white/30 ml-3">
                {item.items.map((subItem: SubMenuItem) => {
                  const isSubItemActive = isActive(subItem.url);
                  return (
                    <SidebarMenuSubItem 
                      key={subItem.title}
                      className={cn(
                        "text-sm rounded-md px-3 py-1.5 transition-colors",
                        isSubItemActive
                          ? "bg-white/20 text-white font-medium" 
                          : "text-white/80 hover:bg-white/10"
                      )}
                    >
                      <Link 
                        to={subItem.url} 
                        onClick={onNavigate}
                        className="flex items-center w-full group"
                      >
                        <span className={cn(
                          "h-1.5 w-1.5 rounded-full mr-3 transition-all duration-200",
                          isSubItemActive 
                            ? "bg-white scale-125" 
                            : "bg-white/40 group-hover:bg-white/60"
                        )} />
                        {subItem.title}
                        {isSubItemActive && (
                          <span className="ml-auto w-1.5 h-1.5 rounded-full bg-white animate-pulse" />
                        )}
                      </Link>
                    </SidebarMenuSubItem>
                  );
                })}
              </SidebarMenuSub>
            </CollapsibleContent>
          )}
        </Collapsible>
      );
    }

    return (
      <SidebarMenuItem
        key={item.title}
        className={cn(
          "transition-all duration-200 hover:bg-white/10",
          isItemActive 
            ? "bg-white/20 text-white" 
            : "text-white/90 hover:text-white"
        )}
      >
        <Link
          to={item.url || '#'}
          onClick={onNavigate}
          className={cn(
            "flex items-center w-full group",
            !isCollapsed && "px-3 py-2 rounded-md"
          )}
        >
          <div className={cn(
            "p-1 rounded-md transition-colors",
            isItemActive 
              ? "bg-white/30 text-white" 
              : "text-white/70 group-hover:bg-white/10"
          )}>
            <Icon className="h-5 w-5 flex-shrink-0" />
          </div>
          {!isCollapsed && (
            <span className="ml-3 text-sm font-medium">{item.title}</span>
          )}
          {isItemActive && !isCollapsed && (
            <span className="ml-auto w-1.5 h-1.5 rounded-full bg-white animate-pulse" />
          )}
        </Link>
      </SidebarMenuItem>
    );
  };

  return (
    <Sidebar 
      className={cn(
        "fixed top-0 left-0 h-screen z-40 transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}
      style={{ backgroundColor: '#071599' }}
      variant="sidebar"
    >
      <SidebarContent className="h-full flex flex-col" style={{ backgroundColor: '#071599' }}>
        <ScrollArea className="flex-1">
          <div className="space-y-1 py-1">
            <SidebarMenu className="space-y-1">
              {filteredItems.map(renderItem)}
            </SidebarMenu>
          </div>
        </ScrollArea>
      </SidebarContent>
    </Sidebar>
  )
}
