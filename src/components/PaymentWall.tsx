
import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { usePayment } from '@/hooks/usePayment'
import { useUserSettings } from '@/hooks/useUserSettings'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  CreditCard, 
  Shield, 
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react'

export function PaymentWall() {
  const { user } = useAuth()
  const { settings, loading: settingsLoading } = useUserSettings(user)
  const { loading, hasStartedPayment, initiatePayment, checkPaymentStatus } = usePayment()
  const [isOpen, setIsOpen] = useState(false)
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'completed' | 'failed' | null>(null)

  // Check payment status on component mount and periodically
  useEffect(() => {
    if (!user || settingsLoading) return

    const checkStatus = async () => {
      const hasPaid = await checkPaymentStatus()
      if (hasPaid) {
        setPaymentStatus('completed')
        setIsOpen(false)
      } else if (!settings?.has_paid) {
        setIsOpen(true)
        setPaymentStatus('pending')
      }
    }

    checkStatus()

    // Check payment status every 10 seconds if payment is pending
    const interval = setInterval(checkStatus, 10000)
    
    return () => clearInterval(interval)
  }, [user, settings, settingsLoading, checkPaymentStatus])

  // Check URL parameters for payment result
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const paymentResult = urlParams.get('payment')
    
    if (paymentResult === 'success') {
      setPaymentStatus('completed')
      setIsOpen(false)
      // Remove parameter from URL
      window.history.replaceState({}, document.title, window.location.pathname)
    } else if (paymentResult === 'failed') {
      setPaymentStatus('failed')
    }
  }, [])

  if (!user || settingsLoading || settings?.has_paid || paymentStatus === 'completed') {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="max-w-md" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <CreditCard className="h-8 w-8 text-primary" />
          </div>
          
          <div className="space-y-2">
            <DialogTitle className="text-xl font-bold">
              Complete Your Setup
            </DialogTitle>
            <DialogDescription className="text-sm">
              A small one-time payment of KES 10 is required to activate your vertiQ account
            </DialogDescription>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {/* Payment Status */}
          {paymentStatus === 'failed' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Payment Failed</span>
              </div>
              <p className="text-xs text-red-600 mt-1">
                Please try again or contact support if the issue persists.
              </p>
            </div>
          )}

          {hasStartedPayment && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-blue-700">
                <Clock className="h-4 w-4" />
                <span className="text-sm font-medium">Payment In Progress</span>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                Complete your payment in the opened window. This dialog will close automatically once payment is successful.
              </p>
            </div>
          )}

          {/* Payment Amount */}
          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">KES 10</div>
            <div className="text-sm text-gray-500">One-time activation fee</div>
            <Badge variant="secondary" className="mt-2">
              Secure Payment via PesaPal
            </Badge>
          </div>

          {/* Benefits */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">What you get:</h4>
            <div className="space-y-2 text-xs text-gray-600">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-3 w-3 text-green-500" />
                <span>Full access to all vertiQ features</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-3 w-3 text-green-500" />
                <span>Unlimited products and transactions</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-3 w-3 text-green-500" />
                <span>Complete business management suite</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-3 w-3 text-blue-500" />
                <span>Secure, encrypted data storage</span>
              </div>
            </div>
          </div>

          {/* Payment Button */}
          <Button 
            onClick={initiatePayment}
            disabled={loading || hasStartedPayment}
            className="w-full"
            size="lg"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Processing...
              </>
            ) : hasStartedPayment ? (
              <>
                <Clock className="h-4 w-4 mr-2" />
                Payment In Progress
              </>
            ) : (
              <>
                <CreditCard className="h-4 w-4 mr-2" />
                Pay KES 10 Now
              </>
            )}
          </Button>

          <p className="text-xs text-center text-gray-500">
            Powered by PesaPal • Secure Payment Processing
          </p>
        </div>
      </DialogContent>
    </Dialog>
  )
}
