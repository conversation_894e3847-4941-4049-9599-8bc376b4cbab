import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useContacts } from "@/hooks/useContacts";
import { useProducts } from "@/hooks/useProducts";
import { useServices } from "@/hooks/useServices";
import { useSubscriptionContext } from "@/components/SubscriptionProvider";
import { Plus, Trash2, Package, Zap } from "lucide-react";
import { formatCurrency } from "@/utils/currency";
import { useCurrency } from "@/contexts/CurrencyContext";

interface ReceiptItem {
  id: string;
  type: 'product' | 'service' | 'manual';
  name: string;
  description?: string;
  quantity: number;
  rate: number;
  amount: number;
  product_id?: string;
  service_id?: string;
  stock_quantity?: number;
}

interface CreateReceiptFormProps {
  onClose: () => void;
  onSubmit: (receiptData: any) => Promise<void>;
}

export function CreateReceiptForm({ onClose, onSubmit }: CreateReceiptFormProps) {
  const { user } = useSubscriptionContext();
  const { contacts } = useContacts();
  const { products, loading: productsLoading } = useProducts(user);
  const { services, loading: servicesLoading } = useServices();
  const { currency } = useCurrency();
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<ReceiptItem[]>([]);
  const [formData, setFormData] = useState({
    receipt_number: `RCP-${Date.now()}`,
    customer: "",
    due_date: "",
    status: "draft",
    notes: "",
    manual_amount: "",
  });

  // Calculate total amount from items
  const totalAmount = items.reduce((sum, item) => sum + item.amount, 0);

  // Debug logging
  useEffect(() => {
    console.log('CreateReceiptForm Debug:', {
      user: user?.id,
      productsCount: products.length,
      servicesCount: services.length,
      productsLoading,
      servicesLoading
    });
  }, [user, products, services, productsLoading, servicesLoading]);

  const addProductItem = (product: any) => {
    const newItem: ReceiptItem = {
      id: `item-${Date.now()}`,
      type: 'product',
      name: product.name,
      description: product.category,
      quantity: 1,
      rate: Number(product.price),
      amount: Number(product.price),
      product_id: product.id,
      stock_quantity: product.stock,
    };
    setItems(prev => [...prev, newItem]);
  };

  const addServiceItem = (service: any) => {
    const newItem: ReceiptItem = {
      id: `item-${Date.now()}`,
      type: 'service',
      name: service.name,
      description: service.description || service.category,
      quantity: 1,
      rate: Number(service.price),
      amount: Number(service.price),
      service_id: service.id,
    };
    setItems(prev => [...prev, newItem]);
  };

  const addManualItem = () => {
    const newItem: ReceiptItem = {
      id: `item-${Date.now()}`,
      type: 'manual',
      name: '',
      description: '',
      quantity: 1,
      rate: 0,
      amount: 0,
    };
    setItems(prev => [...prev, newItem]);
  };

  const updateItem = (itemId: string, field: keyof ReceiptItem, value: any) => {
    setItems(prev => prev.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: value };
        // Recalculate amount when quantity or rate changes
        if (field === 'quantity' || field === 'rate') {
          updatedItem.amount = updatedItem.quantity * updatedItem.rate;
        }
        return updatedItem;
      }
      return item;
    }));
  };

  const removeItem = (itemId: string) => {
    setItems(prev => prev.filter(item => item.id !== itemId));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // If no items are added, use manual amount; otherwise use calculated total
      const finalAmount = items.length > 0 ? totalAmount : parseFloat(formData.manual_amount) || 0;

      const receiptData = {
        ...formData,
        amount: finalAmount,
        items: items.length > 0 ? items : undefined,
      };

      console.log('Submitting receipt:', receiptData);

      await onSubmit(receiptData);
      onClose();
    } catch (error) {
      // Error handled by parent
      console.error('Form submission error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Receipt</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="receipt_number">Receipt Number</Label>
              <Input
                id="receipt_number"
                value={formData.receipt_number}
                onChange={(e) => handleInputChange("receipt_number", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="customer">Customer</Label>
              <Select value={formData.customer} onValueChange={(value) => handleInputChange("customer", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="walk_in">Walk-in Customer</SelectItem>
                  {contacts.map((contact) => (
                    <SelectItem key={contact.id} value={contact.full_name || contact.company}>
                      {contact.full_name || contact.company}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Add Items Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Add Items
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add Product */}
              <div>
                <Label>Add Product from Inventory</Label>
                {productsLoading ? (
                  <div className="text-sm text-muted-foreground">Loading products...</div>
                ) : (
                  <Select onValueChange={(productId) => {
                    const product = products.find(p => p.id === productId);
                    if (product) addProductItem(product);
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder={
                        products.length === 0
                          ? "No products available"
                          : "Select a product"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {products.filter(p => p.status === 'active' || p.status === 'in_stock' || p.status === 'low_stock').map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{product.name}</span>
                            <div className="flex items-center gap-2 ml-4">
                              <Badge variant="outline">{formatCurrency(Number(product.price), currency || 'KES')}</Badge>
                              <Badge variant={product.stock > 0 ? 'default' : 'destructive'}>
                                Stock: {product.stock}
                              </Badge>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                {!productsLoading && products.length === 0 && (
                  <div className="text-sm text-muted-foreground">No products found. Add products in the Inventory page first.</div>
                )}
              </div>

              {/* Add Service */}
              <div>
                <Label>Add Service</Label>
                {servicesLoading ? (
                  <div className="text-sm text-muted-foreground">Loading services...</div>
                ) : (
                  <Select onValueChange={(serviceId) => {
                    const service = services.find(s => s.id === serviceId);
                    if (service) addServiceItem(service);
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder={
                        services.length === 0
                          ? "No services available"
                          : "Select a service"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {services.filter(s => s.status === 'active').map((service) => (
                        <SelectItem key={service.id} value={service.id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{service.name}</span>
                            <Badge variant="outline" className="ml-4">
                              {formatCurrency(Number(service.price), currency || 'KES')}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                {!servicesLoading && services.length === 0 && (
                  <div className="text-sm text-muted-foreground">No services found. Add services in the Services page first.</div>
                )}
              </div>

              {/* Add Manual Item */}
              <Button type="button" variant="outline" onClick={addManualItem} className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Add Manual Item
              </Button>
            </CardContent>
          </Card>

          {/* Items List */}
          {items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Receipt Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {items.map((item) => (
                    <div key={item.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {item.type === 'product' && <Package className="h-4 w-4 text-blue-600" />}
                          {item.type === 'service' && <Zap className="h-4 w-4 text-green-600" />}
                          <Badge variant={item.type === 'manual' ? 'secondary' : 'default'}>
                            {item.type}
                          </Badge>
                          {item.type === 'product' && item.stock_quantity !== undefined && (
                            <Badge variant={item.stock_quantity > 0 ? 'default' : 'destructive'}>
                              Stock: {item.stock_quantity}
                            </Badge>
                          )}
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeItem(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <Label>Name</Label>
                          <Input
                            value={item.name}
                            onChange={(e) => updateItem(item.id, 'name', e.target.value)}
                            disabled={item.type !== 'manual'}
                          />
                        </div>
                        <div>
                          <Label>Description</Label>
                          <Input
                            value={item.description || ''}
                            onChange={(e) => updateItem(item.id, 'description', e.target.value)}
                            disabled={item.type !== 'manual'}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-3">
                        <div>
                          <Label>Quantity</Label>
                          <Input
                            type="number"
                            min="1"
                            max={item.type === 'product' ? item.stock_quantity : undefined}
                            value={item.quantity}
                            onChange={(e) => updateItem(item.id, 'quantity', Number(e.target.value))}
                          />
                        </div>
                        <div>
                          <Label>Rate</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={item.rate}
                            onChange={(e) => updateItem(item.id, 'rate', Number(e.target.value))}
                            disabled={item.type !== 'manual'}
                          />
                        </div>
                        <div>
                          <Label>Amount</Label>
                          <Input
                            value={formatCurrency(item.amount, currency || 'KES')}
                            disabled
                          />
                        </div>
                      </div>
                    </div>
                  ))}

                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center text-lg font-semibold">
                      <span>Total Amount:</span>
                      <span className="text-green-600">
                        {formatCurrency(totalAmount, currency || 'KES')}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Manual Amount (when no items) */}
          {items.length === 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Manual Amount</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Label htmlFor="manual_amount">Receipt Amount</Label>
                  <Input
                    id="manual_amount"
                    type="number"
                    step="0.01"
                    value={formData.manual_amount}
                    onChange={(e) => handleInputChange("manual_amount", e.target.value)}
                    placeholder="Enter receipt amount"
                    required={items.length === 0}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Receipt Details */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="due_date">Due Date</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => handleInputChange("due_date", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="status">Payment Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Additional notes..."
            />
          </div>

          <DialogFooter className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {formData.status === 'paid' && items.length > 0 && (
                <span className="text-amber-600 font-medium">
                  ⚠️ Stock levels and revenue will be updated when receipt is created
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Creating..." : "Create Receipt"}
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
