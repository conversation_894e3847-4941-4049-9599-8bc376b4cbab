import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X, Search, Package, Wrench } from "lucide-react";
import { useCurrency } from "@/contexts/CurrencyContext";
import { formatCurrency } from "@/utils/currency";

interface Item {
  id: string;
  name: string;
  price: number;
  type: 'product' | 'service';
  stock_quantity?: number;
  description?: string;
}

interface Contact {
  id: string;
  name: string;
  email?: string;
  phone?: string;
}

interface AddSaleFormProps {
  onClose: () => void;
  onSubmit: (sale: any) => void;
  products: Item[];
  services: Item[];
  contacts: Contact[];
}

export function AddSaleForm({ onClose, onSubmit, products, services, contacts }: AddSaleFormProps) {
  const { currency } = useCurrency();
  const [step, setStep] = useState<'type' | 'select' | 'details'>('type');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<'product' | 'service'>('product');
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [notes, setNotes] = useState('');
  const [useCustomPrice, setUseCustomPrice] = useState(false);
  const [customPrice, setCustomPrice] = useState('');

  const items = selectedType === 'product' ? products : services;
  const filteredItems = items.filter(item => 
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectItem = (item: Item) => {
    setSelectedItem(item);
    setCustomPrice(item.price.toString());
    setStep('details');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedItem) return;

    const unitPrice = useCustomPrice && customPrice ? parseFloat(customPrice) : selectedItem.price;
    const totalAmount = unitPrice * quantity;
    
    if (isNaN(totalAmount) || totalAmount <= 0) {
      return;
    }
    
    onSubmit({
      itemType: selectedType,
      [selectedType === 'product' ? 'productId' : 'serviceId']: selectedItem.id,
      itemName: selectedItem.name,
      amount: totalAmount,
      unitPrice,
      quantity,
      customerId: selectedCustomer,
      paymentMethod,
      notes,
      date: new Date().toISOString(),
      status: 'completed',
      priceOverridden: useCustomPrice
    });
  };

  const handleBack = () => {
    if (step === 'details') {
      setStep('select');
    } else if (step === 'select') {
      setStep('type');
    } else {
      onClose();
    }
  };

  const renderStep = () => {
    switch (step) {
      case 'type':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium">What are you selling?</h3>
            <div className="grid grid-cols-2 gap-4">
              <Button
                type="button"
                variant={selectedType === 'product' ? 'default' : 'outline'}
                className="h-24 flex-col gap-2"
                onClick={() => {
                  setSelectedType('product');
                  setStep('select');
                }}
              >
                <Package className="h-6 w-6" />
                <span>Product</span>
              </Button>
              <Button
                type="button"
                variant={selectedType === 'service' ? 'default' : 'outline'}
                className="h-24 flex-col gap-2"
                onClick={() => {
                  setSelectedType('service');
                  setStep('select');
                }}
              >
                <Wrench className="h-6 w-6" />
                <span>Service</span>
              </Button>
            </div>
          </div>
        );

      case 'select':
        return (
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={`Search ${selectedType}s...`}
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                autoFocus
              />
            </div>
            <div className="max-h-[200px] overflow-y-auto border rounded-md">
              {filteredItems.length > 0 ? (
                filteredItems.map((item) => (
                  <div
                    key={item.id}
                    className="p-3 hover:bg-accent cursor-pointer border-b last:border-0"
                    onClick={() => handleSelectItem(item)}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{item.name}</span>
                      <span className="text-sm text-muted-foreground">
                        {formatCurrency(item.price, currency || 'KES')}
                        {item.type === 'product' && ` • ${item.stock_quantity} in stock`}
                      </span>
                    </div>
                    {item.description && (
                      <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                    )}
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-muted-foreground">
                  No {selectedType}s found
                </div>
              )}
            </div>
          </div>
        );

      case 'details':
        if (!selectedItem) return null;
        
        return (
          <div className="space-y-4">
            <div className="border rounded-md p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium">{selectedItem.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedItem.type === 'product' ? 'Product' : 'Service'}
                  </p>
                </div>
                <span className="font-medium">
                  {formatCurrency(selectedItem.price, currency || 'KES')}
                  {useCustomPrice && customPrice && (
                    <span className="ml-2 text-sm text-muted-foreground line-through">
                      {formatCurrency(parseFloat(customPrice) || 0, currency || 'KES')}
                    </span>
                  )}
                </span>
              </div>

              <div className="mt-2 flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="useCustomPrice"
                  checked={useCustomPrice}
                  onChange={(e) => {
                    setUseCustomPrice(e.target.checked);
                    if (!e.target.checked) {
                      setCustomPrice(selectedItem.price.toString());
                    }
                  }}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="useCustomPrice" className="text-sm font-normal">
                  Use custom price
                </Label>
              </div>

              {useCustomPrice && (
                <div className="mt-2">
                  <Label htmlFor="customPrice">Custom Price</Label>
                  <Input
                    id="customPrice"
                    type="number"
                    step="0.01"
                    min="0.01"
                    value={customPrice}
                    onChange={(e) => setCustomPrice(e.target.value)}
                    className="mt-1"
                  />
                </div>
              )}
              
              {selectedItem.type === 'product' && (
                <div className="mt-4">
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    max={selectedItem.stock_quantity}
                    value={quantity}
                    onChange={(e) => setQuantity(Math.min(Number(e.target.value), selectedItem.stock_quantity || 1))}
                    className="mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {selectedItem.stock_quantity} available
                  </p>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="customer">Customer</Label>
                <Select value={selectedCustomer} onValueChange={setSelectedCustomer}>
                  <SelectTrigger className="mt-1 w-full">
                    <SelectValue placeholder="Select a customer" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60 overflow-y-auto">
                    {contacts.map((contact) => (
                      <SelectItem key={contact.id} value={contact.id} className="truncate">
                        {contact.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="payment">Payment Method</Label>
                <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                  <SelectTrigger className="mt-1 w-full">
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="card">Credit/Debit Card</SelectItem>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="notes">Notes (Optional)</Label>
                <textarea
                  id="notes"
                  rows={3}
                  className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                />
              </div>

              <div className="bg-muted p-4 rounded-md mt-4">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span className="font-medium">
                    {formatCurrency((useCustomPrice && customPrice ? parseFloat(customPrice) : selectedItem.price) * quantity, currency || 'KES')}
                  </span>
                </div>
                <div className="flex justify-between font-medium mt-2 pt-2 border-t">
                  <span>Total</span>
                  <span>
                    {formatCurrency((useCustomPrice && customPrice ? parseFloat(customPrice) : selectedItem.price) * quantity, currency || 'KES')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-0 sm:p-4" style={{ zIndex: 9999 }}>
      <Card className="w-full h-full sm:h-auto sm:max-h-[90vh] sm:max-w-xl flex flex-col mx-0 sm:mx-4" style={{ zIndex: 10000 }}>
        <CardHeader className="border-b shrink-0 sticky top-0 bg-background z-10 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg sm:text-xl">
              {step === 'type' ? 'New Sale' : 
               step === 'select' ? `Select ${selectedType}` : 'Sale Details'}
            </CardTitle>
            <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <div className="flex-1 overflow-y-auto p-4 sm:p-6">
          <form onSubmit={handleSubmit} className="h-full flex flex-col">
            <div className="space-y-4 sm:space-y-6 flex-1">
              {renderStep()}
            </div>
            <div className="mt-8 pt-4 border-t">
              <div className="flex flex-col sm:flex-row justify-between gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                  className="w-full sm:w-auto"
                >
                  {step === 'type' ? 'Cancel' : 'Back'}
                </Button>
                {step === 'details' ? (
                  <Button 
                    type="submit" 
                    className="w-full sm:w-auto"
                  >
                    Complete Sale
                  </Button>
                ) : (
                  <Button 
                    type="button"
                    variant="default"
                    onClick={() => step === 'select' && selectedItem ? setStep('details') : null}
                    disabled={step === 'select' && !selectedItem}
                    className="w-full sm:w-auto"
                  >
                    {step === 'type' ? 'Continue' : 'Next'}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </div>
      </Card>
    </div>
  );
}
