
import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

type Category = {
  id?: string;
  name: string;
  description: string;
  isActive: boolean;
  parentId?: string;
  imageUrl?: string;
};

type CategoryFormProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  category?: Category;
  onSave: (category: Category) => void;
};

export function CategoryForm({ open, onOpenChange, category, onSave }: CategoryFormProps) {
  const { toast } = useToast();
  const [formData, setFormData] = useState<Category>({
    name: '',
    description: '',
    isActive: true,
    parentId: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [openParentSelect, setOpenParentSelect] = useState(false);
  const [categories, setCategories] = useState<{id: string, name: string}[]>([]);

  // Load categories for parent selection
  useEffect(() => {
    // In a real app, you would fetch this from your API
    const loadCategories = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 300));
        // Filter out the current category if editing
        const fetchedCategories = [
          { id: 'cat-1', name: 'Electronics' },
          { id: 'cat-2', name: 'Clothing' },
          { id: 'cat-3', name: 'Home & Garden' },
          { id: 'cat-4', name: 'Sports & Outdoors' },
          { id: 'cat-5', name: 'Beauty & Personal Care' },
        ].filter(cat => !category || cat.id !== category.id); // Don't allow selecting self as parent
        
        setCategories(fetchedCategories);
        
        // Set initial form data if editing
        if (category) {
          setFormData({
            name: category.name,
            description: category.description || '',
            isActive: category.isActive,
            parentId: category.parentId || '',
          });
        }
      } catch (error) {
        console.error('Failed to load categories', error);
      }
    };
    
    loadCategories();
  }, [category]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onSave(formData);
      toast({
        title: `Category ${category?.id ? 'updated' : 'created'} successfully`,
        description: `${formData.name} has been ${category?.id ? 'updated' : 'added'} to your categories.`,
      });
      onOpenChange(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error saving the category. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (field: keyof Category, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>{category?.id ? 'Edit' : 'Add New'} Category</DialogTitle>
            <DialogDescription>
              {category?.id 
                ? 'Update the category details below.'
                : 'Add a new category to organize your inventory.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Category Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder="e.g., Electronics, Office Supplies"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Enter a brief description of this category"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label>Parent Category (Optional)</Label>
              <Popover open={openParentSelect} onOpenChange={setOpenParentSelect}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={openParentSelect}
                    className="w-full justify-between"
                  >
                    {formData.parentId
                      ? categories.find((category) => category.id === formData.parentId)?.name
                      : "Select parent category..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[300px] p-0" align="start">
                  <Command>
                    <CommandInput placeholder="Search categories..." />
                    <CommandEmpty>No categories found.</CommandEmpty>
                    <CommandGroup>
                      <ScrollArea className="h-[200px] overflow-y-auto">
                        <CommandItem
                          value=""
                          onSelect={() => {
                            handleChange('parentId', '');
                            setOpenParentSelect(false);
                          }}
                          className="cursor-pointer"
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              !formData.parentId ? "opacity-100" : "opacity-0"
                            )}
                          />
                          No parent category
                        </CommandItem>
                        {categories.map((cat) => (
                          <CommandItem
                            key={cat.id}
                            value={cat.name}
                            onSelect={() => {
                              handleChange('parentId', cat.id);
                              setOpenParentSelect(false);
                            }}
                            className="cursor-pointer"
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                formData.parentId === cat.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {cat.name}
                          </CommandItem>
                        ))}
                      </ScrollArea>
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleChange('isActive', checked)}
              />
              <Label htmlFor="isActive">
                {formData.isActive ? 'Active' : 'Inactive'}
              </Label>
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading || !formData.name.trim()}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {category?.id ? 'Updating...' : 'Creating...'}
                </>
              ) : category?.id ? (
                'Update Category'
              ) : (
                'Create Category'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
