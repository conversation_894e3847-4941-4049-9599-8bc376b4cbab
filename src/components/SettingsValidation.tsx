import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, Settings, CheckCircle, X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface CompanySettings {
  company_name?: string;
  avatar_url?: string;
  display_name?: string;
  email?: string;
  currency?: string;
  country?: string;
}

interface SettingsValidationProps {
  settings: CompanySettings;
  showCard?: boolean;
  onDismiss?: () => void;
}

export const SettingsValidation: React.FC<SettingsValidationProps> = ({
  settings,
  showCard = false,
  onDismiss,
}) => {
  const navigate = useNavigate();

  const validateSettings = () => {
    const issues: string[] = [];
    const warnings: string[] = [];

    // Critical issues (prevent invoice generation)
    if (!settings.company_name && !settings.display_name) {
      issues.push('Company Name or Display Name');
    }
    
    if (!settings.email) {
      issues.push('Email Address');
    }

    // Warnings (recommended but not required)
    if (!settings.avatar_url) {
      warnings.push('Company Logo');
    }

    if (!settings.country) {
      warnings.push('Country/Location');
    }

    return {
      hasIssues: issues.length > 0,
      hasWarnings: warnings.length > 0,
      issues,
      warnings,
      isComplete: issues.length === 0 && warnings.length === 0,
    };
  };

  const validation = validateSettings();

  const handleGoToSettings = () => {
    navigate('/settings');
  };

  if (validation.isComplete) {
    return showCard ? (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-green-700">
            <CheckCircle className="h-5 w-5" />
            <span className="font-medium">Company settings are complete</span>
            {onDismiss && (
              <Button variant="ghost" size="icon" onClick={onDismiss} className="ml-auto h-6 w-6">
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    ) : null;
  }

  if (showCard) {
    return (
      <Card className={`border-2 ${validation.hasIssues ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'}`}>
        <CardHeader className="pb-3">
          <CardTitle className={`flex items-center gap-2 text-lg ${validation.hasIssues ? 'text-red-700' : 'text-yellow-700'}`}>
            <AlertTriangle className="h-5 w-5" />
            {validation.hasIssues ? 'Company Settings Required' : 'Company Settings Incomplete'}
            {onDismiss && (
              <Button variant="ghost" size="icon" onClick={onDismiss} className="ml-auto h-6 w-6">
                <X className="h-4 w-4" />
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {validation.hasIssues && (
            <div>
              <p className="font-medium text-red-700 mb-2">
                Required for professional invoices:
              </p>
              <ul className="list-disc list-inside space-y-1 text-red-600">
                {validation.issues.map((issue, index) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </div>
          )}

          {validation.hasWarnings && (
            <div>
              <p className="font-medium text-yellow-700 mb-2">
                Recommended for better branding:
              </p>
              <ul className="list-disc list-inside space-y-1 text-yellow-600">
                {validation.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          )}

          <Button 
            onClick={handleGoToSettings}
            className={`w-full ${validation.hasIssues ? 'bg-red-600 hover:bg-red-700' : 'bg-yellow-600 hover:bg-yellow-700'} text-white`}
          >
            <Settings className="mr-2 h-4 w-4" />
            Configure Settings
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Inline alert version
  return (
    <Alert className={`${validation.hasIssues ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'}`}>
      <AlertTriangle className={`h-4 w-4 ${validation.hasIssues ? 'text-red-600' : 'text-yellow-600'}`} />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <span className={`font-medium ${validation.hasIssues ? 'text-red-700' : 'text-yellow-700'}`}>
            {validation.hasIssues ? 'Company settings required: ' : 'Company settings incomplete: '}
          </span>
          <span className={validation.hasIssues ? 'text-red-600' : 'text-yellow-600'}>
            {[...validation.issues, ...validation.warnings].join(', ')}
          </span>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleGoToSettings}
          className={`ml-4 ${validation.hasIssues ? 'border-red-300 text-red-700 hover:bg-red-100' : 'border-yellow-300 text-yellow-700 hover:bg-yellow-100'}`}
        >
          <Settings className="mr-1 h-3 w-3" />
          Fix
        </Button>
      </AlertDescription>
    </Alert>
  );
};

// Hook for easy validation checking
export const useSettingsValidation = (settings: CompanySettings) => {
  const validateSettings = () => {
    const issues: string[] = [];
    const warnings: string[] = [];

    if (!settings.company_name && !settings.display_name) {
      issues.push('Company Name or Display Name');
    }
    
    if (!settings.email) {
      issues.push('Email Address');
    }

    if (!settings.avatar_url) {
      warnings.push('Company Logo');
    }

    if (!settings.country) {
      warnings.push('Country/Location');
    }

    return {
      hasIssues: issues.length > 0,
      hasWarnings: warnings.length > 0,
      issues,
      warnings,
      isComplete: issues.length === 0 && warnings.length === 0,
      canGenerateInvoices: issues.length === 0, // Can generate with warnings, but not with issues
    };
  };

  return validateSettings();
};
