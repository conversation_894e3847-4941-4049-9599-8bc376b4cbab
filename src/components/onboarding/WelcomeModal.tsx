import React from 'react';
import { useOnboarding } from './OnboardingProvider';
import { useAuth } from '@/contexts/AuthContext';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Rocket, 
  BarChart3, 
  ShoppingCart, 
  Package, 
  Users, 
  FileText,
  ArrowRight,
  Clock
} from 'lucide-react';

const features = [
  {
    icon: BarChart3,
    title: 'Dashboard Analytics',
    description: 'Track revenue, expenses, and business performance'
  },
  {
    icon: Package,
    title: 'Inventory Management',
    description: 'Manage products, stock levels, and categories'
  },
  {
    icon: ShoppingCart,
    title: 'Point of Sale',
    description: 'Process sales quickly and efficiently'
  },
  {
    icon: Users,
    title: 'Customer Management',
    description: 'Manage contacts, companies, and relationships'
  },
  {
    icon: FileText,
    title: 'Receipts & Quotes',
    description: 'Generate professional documents'
  }
];

export function WelcomeModal() {
  const { isNewUser, startOnboarding, skipOnboarding } = useOnboarding();
  const { user } = useAuth();
  const [isOpen, setIsOpen] = React.useState(false);

  React.useEffect(() => {
    if (isNewUser && user) {
      const hasSeenWelcome = localStorage.getItem(`welcome_seen_${user.id}`);
      if (!hasSeenWelcome) {
        setTimeout(() => {
          setIsOpen(true);
        }, 1000);
      }
    }
  }, [isNewUser, user]);

  const handleStartTour = () => {
    if (user?.id) {
      localStorage.setItem(`welcome_seen_${user.id}`, 'true');
    }
    setIsOpen(false);
    startOnboarding();
  };

  const handleSkipTour = () => {
    if (user?.id) {
      localStorage.setItem(`welcome_seen_${user.id}`, 'true');
    }
    setIsOpen(false);
    skipOnboarding();
  };

  if (!isNewUser) return null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-2xl">
        <DialogHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <Rocket className="h-8 w-8 text-primary" />
          </div>
          
          <div className="space-y-2">
            <DialogTitle className="text-2xl font-bold">
              Welcome to vertiQ! 🎉
            </DialogTitle>
            <DialogDescription className="text-base">
              Your complete business management solution is ready to go.
              Let's take a quick tour to help you get started.
            </DialogDescription>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Icon className="h-4 w-4 text-primary" />
                  </div>
                  <div className="space-y-1">
                    <h4 className="font-medium text-sm">{feature.title}</h4>
                    <p className="text-xs text-muted-foreground">{feature.description}</p>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Tour Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900 text-sm">Quick Tour</span>
              <Badge variant="secondary" className="text-xs">5 minutes</Badge>
            </div>
            <p className="text-sm text-blue-700">
              We'll guide you through the key features step-by-step. You can skip or pause anytime.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button 
              onClick={handleStartTour}
              className="flex-1 gap-2"
              size="lg"
            >
              <Rocket className="h-4 w-4" />
              Start the Tour
              <ArrowRight className="h-4 w-4" />
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleSkipTour}
              className="flex-1"
              size="lg"
            >
              Skip for Now
            </Button>
          </div>

          <p className="text-xs text-center text-muted-foreground">
            You can always restart the tour from Settings → Help & Support
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
