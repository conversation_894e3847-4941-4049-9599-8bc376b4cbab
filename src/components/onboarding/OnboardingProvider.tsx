import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  target?: string; // CSS selector for element to highlight
  page: string; // Which page this step belongs to
  action?: 'click' | 'input' | 'navigate' | 'observe';
  actionTarget?: string;
  completed: boolean;
}

interface OnboardingContextType {
  isOnboardingActive: boolean;
  currentStep: OnboardingStep | null;
  currentStepIndex: number;
  totalSteps: number;
  isNewUser: boolean;
  startOnboarding: () => void;
  nextStep: () => void;
  previousStep: () => void;
  skipOnboarding: () => void;
  completeStep: (stepId: string) => void;
  goToStep: (stepIndex: number) => void;
  onboardingSteps: OnboardingStep[];
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to vertiQ!',
    description: 'Let\'s take a quick tour to help you get started with your business management system.',
    page: 'dashboard',
    completed: false
  },
  {
    id: 'dashboard-overview',
    title: 'Your Dashboard',
    description: 'This is your business command center. Here you can see your revenue, expenses, and key metrics at a glance.',
    target: '.dashboard-metrics',
    page: 'dashboard',
    action: 'observe',
    completed: false
  },
  {
    id: 'add-first-product',
    title: 'Add Your First Product',
    description: 'Let\'s start by adding a product to your inventory. Click on "Inventory" in the sidebar.',
    target: '[href="/inventory"]',
    page: 'dashboard',
    action: 'navigate',
    actionTarget: '/inventory',
    completed: false
  },
  {
    id: 'inventory-page',
    title: 'Inventory Management',
    description: 'Here you can manage all your products, track stock levels, and monitor inventory value.',
    page: 'inventory',
    action: 'observe',
    completed: false
  },
  {
    id: 'add-product-button',
    title: 'Add a Product',
    description: 'Click the "Add Product" button to create your first product.',
    target: '[data-testid="add-product-button"]',
    page: 'inventory',
    action: 'click',
    completed: false
  },
  {
    id: 'product-form',
    title: 'Product Information',
    description: 'Fill in your product details. Don\'t worry, you can always edit this later!',
    page: 'inventory',
    action: 'input',
    completed: false
  },
  {
    id: 'pos-introduction',
    title: 'Point of Sale (POS)',
    description: 'Now let\'s explore the POS system where you can make sales. Click on "POS" in the sidebar.',
    target: '[href="/pos"]',
    page: 'inventory',
    action: 'navigate',
    actionTarget: '/pos',
    completed: false
  },
  {
    id: 'pos-overview',
    title: 'Making Sales',
    description: 'This is where you process sales. You can search for products, add them to cart, and complete transactions.',
    page: 'pos',
    action: 'observe',
    completed: false
  },
  {
    id: 'sales-tracking',
    title: 'Sales Management',
    description: 'Let\'s check out the Sales page to see how your transactions are tracked.',
    target: '[href="/sales"]',
    page: 'pos',
    action: 'navigate',
    actionTarget: '/sales',
    completed: false
  },
  {
    id: 'expenses-intro',
    title: 'Track Your Expenses',
    description: 'Don\'t forget to track your business expenses! Click on "Expenses" to learn more.',
    target: '[href="/expenses"]',
    page: 'sales',
    action: 'navigate',
    actionTarget: '/expenses',
    completed: false
  },
  {
    id: 'settings-profile',
    title: 'Complete Your Profile',
    description: 'Finally, let\'s complete your business profile. Click on "Settings" to add your company details.',
    target: '[href="/settings"]',
    page: 'expenses',
    action: 'navigate',
    actionTarget: '/settings',
    completed: false
  },
  {
    id: 'onboarding-complete',
    title: 'You\'re All Set!',
    description: 'Congratulations! You\'ve completed the onboarding tour. You\'re ready to start managing your business with vertiQ.',
    page: 'settings',
    completed: false
  }
];

export function OnboardingProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [isOnboardingActive, setIsOnboardingActive] = useState(false);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [onboardingSteps, setOnboardingSteps] = useState<OnboardingStep[]>(ONBOARDING_STEPS);
  const [isNewUser, setIsNewUser] = useState(false);

  // Check if user is new (registered within last 24 hours)
  useEffect(() => {
    if (user?.created_at) {
      const createdAt = new Date(user.created_at);
      const now = new Date();
      const hoursSinceCreation = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
      
      const isNew = hoursSinceCreation < 24;
      setIsNewUser(isNew);
      
      // Auto-start onboarding for new users
      if (isNew) {
        const hasCompletedOnboarding = localStorage.getItem(`onboarding_completed_${user.id}`);
        if (!hasCompletedOnboarding) {
          setTimeout(() => {
            setIsOnboardingActive(true);
          }, 2000); // Start after 2 seconds to let the page load
        }
      }
    }
  }, [user]);

  const currentStep = onboardingSteps[currentStepIndex] || null;

  const startOnboarding = () => {
    setIsOnboardingActive(true);
    setCurrentStepIndex(0);
  };

  const nextStep = () => {
    if (currentStepIndex < onboardingSteps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      completeOnboarding();
    }
  };

  const previousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const goToStep = (stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < onboardingSteps.length) {
      setCurrentStepIndex(stepIndex);
    }
  };

  const completeStep = (stepId: string) => {
    setOnboardingSteps(steps => 
      steps.map(step => 
        step.id === stepId ? { ...step, completed: true } : step
      )
    );
  };

  const skipOnboarding = () => {
    completeOnboarding();
  };

  const completeOnboarding = () => {
    setIsOnboardingActive(false);
    if (user?.id) {
      localStorage.setItem(`onboarding_completed_${user.id}`, 'true');
    }
  };

  const value: OnboardingContextType = {
    isOnboardingActive,
    currentStep,
    currentStepIndex,
    totalSteps: onboardingSteps.length,
    isNewUser,
    startOnboarding,
    nextStep,
    previousStep,
    skipOnboarding,
    completeStep,
    goToStep,
    onboardingSteps
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
