import React, { useEffect, useState, useRef } from 'react';
import { useOnboarding } from './OnboardingProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { X, ArrowLeft, ArrowRight, SkipForward, Play } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';

export function OnboardingTooltip() {
  const {
    isOnboardingActive,
    currentStep,
    currentStepIndex,
    totalSteps,
    nextStep,
    previousStep,
    skipOnboarding,
    completeStep
  } = useOnboarding();
  
  const navigate = useNavigate();
  const location = useLocation();
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Calculate tooltip position based on target element
  useEffect(() => {
    if (!isOnboardingActive || !currentStep) return;

    const updatePosition = () => {
      if (currentStep.target) {
        const targetElement = document.querySelector(currentStep.target);
        if (targetElement) {
          const rect = targetElement.getBoundingClientRect();
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
          
          // Position tooltip below the target element
          setTooltipPosition({
            top: rect.bottom + scrollTop + 10,
            left: rect.left + scrollLeft + (rect.width / 2) - 200 // Center the tooltip
          });
          
          // Highlight the target element
          targetElement.classList.add('onboarding-highlight');
          
          // Scroll target into view
          targetElement.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'center'
          });
        }
      } else {
        // Center tooltip on screen if no target
        setTooltipPosition({
          top: window.innerHeight / 2 - 100,
          left: window.innerWidth / 2 - 200
        });
      }
      setIsVisible(true);
    };

    // Small delay to ensure DOM is ready
    const timer = setTimeout(updatePosition, 100);
    
    // Update position on resize
    window.addEventListener('resize', updatePosition);
    
    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', updatePosition);
      
      // Remove highlight from all elements
      document.querySelectorAll('.onboarding-highlight').forEach(el => {
        el.classList.remove('onboarding-highlight');
      });
    };
  }, [isOnboardingActive, currentStep]);

  // Handle navigation for steps that require page changes
  useEffect(() => {
    if (!currentStep || !isOnboardingActive) return;

    if (currentStep.action === 'navigate' && currentStep.actionTarget) {
      const currentPage = location.pathname;
      if (currentPage !== currentStep.actionTarget) {
        navigate(currentStep.actionTarget);
      }
    }
  }, [currentStep, navigate, location.pathname, isOnboardingActive]);

  // Auto-advance for certain step types
  useEffect(() => {
    if (!currentStep || !isOnboardingActive) return;

    if (currentStep.action === 'observe') {
      // Auto-advance after 3 seconds for observation steps
      const timer = setTimeout(() => {
        completeStep(currentStep.id);
        nextStep();
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [currentStep, isOnboardingActive, completeStep, nextStep]);

  const handleNext = () => {
    if (currentStep) {
      completeStep(currentStep.id);
    }
    nextStep();
  };

  const handlePrevious = () => {
    previousStep();
  };

  const handleSkip = () => {
    skipOnboarding();
  };

  if (!isOnboardingActive || !currentStep || !isVisible) {
    return null;
  }

  const progress = ((currentStepIndex + 1) / totalSteps) * 100;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50 pointer-events-none" />
      
      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className="fixed z-[60] w-96 pointer-events-auto"
        style={{
          top: tooltipPosition.top,
          left: Math.max(10, Math.min(tooltipPosition.left, window.innerWidth - 400))
        }}
      >
        <Card className="shadow-2xl border-2 border-primary/20">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <Badge variant="secondary" className="text-xs">
                Step {currentStepIndex + 1} of {totalSteps}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSkip}
                className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <CardTitle className="text-lg">{currentStep.title}</CardTitle>
            <Progress value={progress} className="h-2" />
          </CardHeader>
          
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground leading-relaxed">
              {currentStep.description}
            </p>
            
            {currentStep.action === 'click' && (
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 text-blue-700">
                  <Play className="h-4 w-4" />
                  <span className="text-sm font-medium">Action Required</span>
                </div>
                <p className="text-xs text-blue-600 mt-1">
                  Click the highlighted element to continue
                </p>
              </div>
            )}
            
            {currentStep.action === 'input' && (
              <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 text-green-700">
                  <Play className="h-4 w-4" />
                  <span className="text-sm font-medium">Fill in the form</span>
                </div>
                <p className="text-xs text-green-600 mt-1">
                  Complete the form fields and save
                </p>
              </div>
            )}
            
            <div className="flex justify-between items-center pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                disabled={currentStepIndex === 0}
                className="gap-1"
              >
                <ArrowLeft className="h-3 w-3" />
                Previous
              </Button>
              
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSkip}
                  className="gap-1"
                >
                  <SkipForward className="h-3 w-3" />
                  Skip Tour
                </Button>
                
                <Button
                  size="sm"
                  onClick={handleNext}
                  className="gap-1"
                >
                  {currentStepIndex === totalSteps - 1 ? 'Finish' : 'Next'}
                  <ArrowRight className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
