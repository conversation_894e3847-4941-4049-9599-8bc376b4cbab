
import React from 'react';
import { useNavigate } from 'react-router-dom';

export function Logo() {
  const navigate = useNavigate();

  return (
    <div 
      className="flex items-center gap-3 cursor-pointer group"
      onClick={() => navigate('/')}
      aria-label="VertiQ Home"
    >
      <div className="relative flex-shrink-0">
        <svg 
          width="36" 
          height="36" 
          viewBox="0 0 40 40" 
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
          className="text-purple-600"
        >
          {/* Outer circle */}
          <circle cx="20" cy="20" r="18" fill="currentColor" opacity="0.1" />
          {/* Middle ring */}
          <circle cx="20" cy="20" r="14" stroke="currentColor" strokeWidth="1.5" opacity="0.5" />
          {/* Inner ring */}
          <circle cx="20" cy="20" r="8" stroke="currentColor" strokeWidth="1.5" opacity="0.8" />
          {/* Center dot */}
          <circle cx="20" cy="20" r="3" fill="currentColor" />
        </svg>
      </div>
      <span className="text-2xl font-bold text-gray-900 tracking-tight">
        VertiQ
      </span>
    </div>
  );
}

export function LogoText() {
  const navigate = useNavigate();

  return (
    <div 
      className="flex items-center gap-3 cursor-pointer group"
      onClick={() => navigate('/')}
      aria-label="vertiQ Home"
    >
      <div className="relative">
        <div className="absolute -inset-1 bg-gradient-to-r from-white/20 to-white/10 rounded-full blur opacity-75 group-hover:opacity-100 transition-opacity"></div>
        <div className="relative flex items-center justify-center w-10 h-10 bg-white rounded-full shadow-lg">
          <svg 
            width="20" 
            height="20" 
            viewBox="0 0 24 24" 
            fill="none" 
            xmlns="http://www.w3.org/2000/svg"
            className="text-blue-600"
          >
            <path 
              d="M12 2L2 7L12 12L22 7L12 2Z" 
              fill="currentColor"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path 
              d="M2 17L12 22L22 17" 
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path 
              d="M2 12L12 17L22 12" 
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
      <div className="flex flex-col">
        <span className="text-xl font-bold leading-tight text-white">vertiQ</span>
        <span className="text-xs text-white/80 leading-none">Business Management</span>
      </div>
    </div>
  );
}
