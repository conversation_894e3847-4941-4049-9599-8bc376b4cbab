
import { useState, useEffect } from "react";
import { AppSidebar } from "@/components/AppSidebar";
import { Header } from "@/components/Header";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { BottomNav } from "@/components/BottomNav";
import { useLocation } from "react-router-dom";
import type { User } from "@supabase/supabase-js";

interface LayoutProps {
  children: React.ReactNode;
  user?: User | null;
}

export function Layout({ children, user }: LayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const isMobile = useIsMobile();
  const location = useLocation();

  // Close mobile menu when resizing to desktop
  useEffect(() => {
    if (!isMobile) {
      setIsMobileMenuOpen(false);
    }
  }, [isMobile]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleSidebar = () => {
    if (!isMobile) {
      setIsSidebarCollapsed(!isSidebarCollapsed);
    }
  };

  return (
    <div className="min-h-screen w-full flex flex-col bg-background">
      {/* Header - Fixed at top */}
      <Header
        onMenuClick={toggleMobileMenu}
        isMobileMenuOpen={isMobileMenuOpen}
        isSidebarCollapsed={isSidebarCollapsed}
        user={user}
      />

      <div className="flex flex-1 relative">
        {/* Sidebar Backdrop for Mobile */}
        {isMobile && isMobileMenuOpen && (
          <div 
            className="fixed inset-0 z-40 bg-black/50 transition-opacity duration-300"
            onClick={() => setIsMobileMenuOpen(false)}
            aria-hidden="true"
          />
        )}

        {/* Sidebar - Only show on desktop or when mobile menu is open */}
        {(!isMobile || isMobileMenuOpen) && (
          <aside 
            className={cn(
              "fixed inset-y-0 top-16 z-50 flex h-[calc(100vh-4rem)] flex-col border-r bg-gradient-to-b from-blue-600 to-blue-700 text-white shadow-lg transition-all duration-300 ease-in-out overflow-hidden",
              isMobile ? "left-0 w-64" : "left-0",
              isMobile ? 
                isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full' :
                'translate-x-0',
              !isMobile && isSidebarCollapsed && 'w-16',
              !isMobile && !isSidebarCollapsed && 'w-64'
            )}
            aria-label="Sidebar"
          >
            <AppSidebar 
              onNavigate={() => setIsMobileMenuOpen(false)}
              isCollapsed={!isMobile && isSidebarCollapsed}
              onCollapse={setIsSidebarCollapsed}
            />
          </aside>
        )}

        {/* Main Content */}
        <main 
          className={cn(
            "flex-1 flex flex-col min-h-[calc(100vh-4rem)] mt-16 overflow-auto bg-gray-50",
            // Desktop spacing
            !isMobile && !isSidebarCollapsed && "ml-64",
            !isMobile && isSidebarCollapsed && "ml-16",
            // Mobile spacing - no left margin since sidebar is overlay
            isMobile && "ml-0",
            // Bottom padding for mobile bottom nav
            isMobile && "pb-16"
          )}
        >
          <div className="flex-1 p-4 md:p-6 lg:p-8 max-w-7xl mx-auto w-full">
            {children}
          </div>
          
          {/* Footer - Only on desktop */}
          {!isMobile && (
            <footer className="border-t py-4 px-6 text-center text-sm text-muted-foreground bg-background/80 backdrop-blur-sm">
              <p>© {new Date().getFullYear()} Business Management System. All rights reserved.</p>
            </footer>
          )}
        </main>
      </div>
      
      {/* Bottom Navigation - Mobile only */}
      {isMobile && <BottomNav />}
    </div>
  );
}
