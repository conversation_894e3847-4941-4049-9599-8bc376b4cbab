import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { InvoiceTemplate } from './InvoiceTemplate';
import { useInvoicePrint } from '@/hooks/useInvoicePrint';
import { useUserSettings } from '@/hooks/useUserSettings';
import { useSubscriptionContext } from '@/components/SubscriptionProvider';
import { Printer, Download, Eye, X, AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface InvoiceData {
  id: string;
  invoice_number: string;
  customer: string;
  due_date: string;
  amount: number;
  status: string;
  items?: any[];
  notes?: string;
  created_at: string;
}

interface InvoiceViewerProps {
  invoice: InvoiceData;
  isOpen: boolean;
  onClose: () => void;
}

export const InvoiceViewer: React.FC<InvoiceViewerProps> = ({
  invoice,
  isOpen,
  onClose,
}) => {
  const { user } = useSubscriptionContext();
  const { settings, loading: settingsLoading } = useUserSettings(user);
  const {
    componentRef,
    handlePrint,
    handleDownloadPDF,
    validateCompanySettings,
    showSettingsWarning,
  } = useInvoicePrint();

  const companySettings = {
    company_name: settings?.company_name,
    avatar_url: settings?.avatar_url,
    display_name: settings?.display_name,
    email: settings?.email,
    currency: settings?.user_currency || settings?.currency,
    country: settings?.country,
  };

  const { isValid: settingsValid, missingFields } = validateCompanySettings(companySettings);

  const handlePrintClick = () => {
    if (!settingsValid) {
      showSettingsWarning(missingFields);
      return;
    }
    handlePrint();
  };

  const handleDownloadClick = () => {
    if (!settingsValid) {
      showSettingsWarning(missingFields);
      return;
    }
    handleDownloadPDF(invoice, companySettings);
  };

  if (settingsLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Loading Invoice...</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[95vh] overflow-y-auto p-0">
        <DialogHeader className="p-6 pb-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Invoice #{invoice.invoice_number}
            </DialogTitle>
            <div className="flex items-center gap-2">
              {!settingsValid && (
                <Badge variant="destructive" className="flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  Settings Incomplete
                </Badge>
              )}
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Action Buttons */}
        <div className="px-6 pb-4">
          <div className="flex items-center gap-3">
            <Button
              onClick={handlePrintClick}
              className="flex items-center gap-2"
              variant="outline"
            >
              <Printer className="h-4 w-4" />
              Print Invoice
            </Button>
            <Button
              onClick={handleDownloadClick}
              className="flex items-center gap-2"
              variant="outline"
            >
              <Download className="h-4 w-4" />
              Download PDF
            </Button>
          </div>
          
          {!settingsValid && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-800">
                    Company settings incomplete
                  </p>
                  <p className="text-yellow-700">
                    Please configure the following in Settings to generate professional invoices: {missingFields.join(', ')}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Invoice Template */}
        <div className="px-6 pb-6">
          <Card className="shadow-lg">
            <CardContent className="p-0">
              <InvoiceTemplate
                ref={componentRef}
                invoice={invoice}
                companySettings={companySettings}
              />
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
