
import React, { createContext, useContext, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';
import { usePackagesPopup } from '@/hooks/usePackagesPopup';
import { PackagesPopup } from '@/components/PackagesPopup';

interface SubscriptionContextType {
  user: User | null;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const useSubscriptionContext = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscriptionContext must be used within a SubscriptionProvider');
  }
  return context;
};

interface SubscriptionProviderProps {
  children: ReactNode;
  user: User | null; // Receive user from parent App component
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children, user }) => {
  // Use packages popup hook for the target user
  const { shouldShowPopup, dismissPopup, isTargetUser } = usePackagesPopup(user?.email);

  const contextValue: SubscriptionContextType = {
    user,
  };

  return (
    <SubscriptionContext.Provider value={contextValue}>
      {children}

      {/* Show packages popup <NAME_EMAIL> */}
      {shouldShowPopup && isTargetUser && (
        <PackagesPopup
          isOpen={true}
          onDismiss={dismissPopup}
        />
      )}
    </SubscriptionContext.Provider>
  );
};
