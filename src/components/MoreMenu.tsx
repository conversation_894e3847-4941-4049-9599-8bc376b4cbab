import { NavLink } from "react-router-dom";
import {
  TrendingUp,
  Package,
  Wrench,
  FileText,
  Quote,
  Calculator,
  Settings,
  User,
  X,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

const items = [
  { title: "Profit & Loss", url: "/profit-loss", icon: TrendingUp },
  { title: "Inventory", url: "/inventory", icon: Package },
  { title: "Services", url: "/services", icon: Wrench },
  { title: "Receipts", url: "/receipts", icon: FileText },
  { title: "Quotes", url: "/quotes", icon: Quote },
  { title: "Financial Runway", url: "/financial-runway", icon: Calculator },
  { title: "Profile", url: "/profile", icon: User },
  { title: "Settings", url: "/settings", icon: Settings },
];

export function MoreMenu() {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <button className="flex flex-col items-center justify-center w-full h-full space-y-1 text-gray-600">
          <TrendingUp size={20} />
          <span className="text-xs">More</span>
        </button>
      </SheetTrigger>
      <SheetContent side="bottom" className="h-[80vh]">
        <SheetHeader className="flex flex-row items-center justify-between">
          <SheetTitle>More Options</SheetTitle>
          <SheetTrigger asChild>
            <button className="p-2 hover:bg-gray-100 rounded-lg">
              <X size={20} />
            </button>
          </SheetTrigger>
        </SheetHeader>
        <div className="mt-6 space-y-2">
          {items.map((item) => (
            <NavLink
              key={item.title}
              to={item.url}
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                  isActive
                    ? "bg-blue-100 text-blue-700 font-medium"
                    : "text-gray-700 hover:bg-gray-100"
                }`
              }
            >
              <item.icon size={20} />
              <span>{item.title}</span>
            </NavLink>
          ))}
        </div>
      </SheetContent>
    </Sheet>
  );
} 