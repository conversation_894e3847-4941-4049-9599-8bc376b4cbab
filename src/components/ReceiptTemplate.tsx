import React, { forwardRef } from 'react';
import { formatCurrency } from '@/utils/currency';
import { format } from 'date-fns';

interface ReceiptItem {
  description: string;
  quantity: number;
  rate: number;
}

interface ReceiptData {
  id: string;
  receipt_number?: string;
  invoice_number?: string; // For backward compatibility
  customer: string;
  due_date: string;
  amount: number;
  status: string;
  items: ReceiptItem[];
  notes?: string;
  created_at: string;
}

interface CompanySettings {
  company_name?: string;
  avatar_url?: string;
  display_name?: string;
  email?: string;
  currency?: string;
  country?: string;
}

interface ReceiptTemplateProps {
  receipt: ReceiptData;
  companySettings: CompanySettings;
}

export const ReceiptTemplate = forwardRef<HTMLDivElement, ReceiptTemplateProps>(
  ({ receipt, companySettings }, ref) => {
    const subtotal = receipt.items?.reduce((sum, item) => sum + (item.quantity * item.rate), 0) || receipt.amount;
    const tax = 0; // You can add tax calculation logic here
    const total = subtotal + tax;

    return (
      <div ref={ref} className="receipt-template bg-white p-8 max-w-4xl mx-auto">
        {/* Print-specific styles */}
        <style jsx>{`
          @media print {
            .receipt-template {
              margin: 0;
              padding: 20px;
              box-shadow: none;
              max-width: none;
            }
            .no-print {
              display: none !important;
            }
            body {
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }
          }
        `}</style>

        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div className="flex items-center space-x-4">
            {companySettings.avatar_url && (
              <img
                src={companySettings.avatar_url}
                alt="Company Logo"
                className="w-16 h-16 object-contain"
              />
            )}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {companySettings.company_name || companySettings.display_name || 'Your Company'}
              </h1>
              {companySettings.email && (
                <p className="text-gray-600">{companySettings.email}</p>
              )}
              {companySettings.country && (
                <p className="text-gray-600">{companySettings.country}</p>
              )}
            </div>
          </div>
          <div className="text-right">
            <h2 className="text-3xl font-bold text-green-600 mb-2">RECEIPT</h2>
            <p className="text-gray-600">#{receipt.receipt_number || receipt.invoice_number}</p>
          </div>
        </div>

        {/* Receipt Details */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Received From:</h3>
            <div className="text-gray-700">
              <p className="font-medium">{receipt.customer}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Receipt Date:</span>
                <span className="font-medium">
                  {format(new Date(receipt.created_at), 'MMM dd, yyyy')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Due Date:</span>
                <span className="font-medium">
                  {format(new Date(receipt.due_date), 'MMM dd, yyyy')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium capitalize px-2 py-1 rounded text-sm ${
                  receipt.status === 'paid' ? 'bg-green-100 text-green-800' :
                  receipt.status === 'overdue' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {receipt.status}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Items Table */}
        <div className="mb-8">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b-2 border-gray-300">
                <th className="text-left py-3 px-2 font-semibold text-gray-900">Description</th>
                <th className="text-center py-3 px-2 font-semibold text-gray-900 w-20">Qty</th>
                <th className="text-right py-3 px-2 font-semibold text-gray-900 w-24">Rate</th>
                <th className="text-right py-3 px-2 font-semibold text-gray-900 w-24">Amount</th>
              </tr>
            </thead>
            <tbody>
              {receipt.items && receipt.items.length > 0 ? (
                receipt.items.map((item, index) => (
                  <tr key={index} className="border-b border-gray-200">
                    <td className="py-3 px-2 text-gray-700">{item.description}</td>
                    <td className="py-3 px-2 text-center text-gray-700">{item.quantity}</td>
                    <td className="py-3 px-2 text-right text-gray-700">
                      {formatCurrency(item.rate, companySettings.currency || 'KES')}
                    </td>
                    <td className="py-3 px-2 text-right text-gray-700 font-medium">
                      {formatCurrency(item.quantity * item.rate, companySettings.currency || 'KES')}
                    </td>
                  </tr>
                ))
              ) : (
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-2 text-gray-700">Service/Product</td>
                  <td className="py-3 px-2 text-center text-gray-700">1</td>
                  <td className="py-3 px-2 text-right text-gray-700">
                    {formatCurrency(receipt.amount, companySettings.currency || 'KES')}
                  </td>
                  <td className="py-3 px-2 text-right text-gray-700 font-medium">
                    {formatCurrency(receipt.amount, companySettings.currency || 'KES')}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Totals */}
        <div className="flex justify-end mb-8">
          <div className="w-64">
            <div className="space-y-2">
              <div className="flex justify-between py-2">
                <span className="text-gray-600">Subtotal:</span>
                <span className="font-medium">
                  {formatCurrency(subtotal, companySettings.currency || 'KES')}
                </span>
              </div>
              {tax > 0 && (
                <div className="flex justify-between py-2">
                  <span className="text-gray-600">Tax:</span>
                  <span className="font-medium">
                    {formatCurrency(tax, companySettings.currency || 'KES')}
                  </span>
                </div>
              )}
              <div className="flex justify-between py-3 border-t-2 border-gray-300">
                <span className="text-lg font-semibold text-gray-900">Total Received:</span>
                <span className="text-lg font-bold text-green-600">
                  {formatCurrency(total, companySettings.currency || 'KES')}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Notes */}
        {receipt.notes && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Notes:</h3>
            <p className="text-gray-700 whitespace-pre-wrap">{receipt.notes}</p>
          </div>
        )}

        {/* Footer */}
        <div className="border-t border-gray-300 pt-6 text-center text-gray-600">
          <p>Thank you for your payment!</p>
          {companySettings.email && (
            <p className="mt-2">
              For questions about this receipt, please contact us at {companySettings.email}
            </p>
          )}
        </div>
      </div>
    );
  }
);

ReceiptTemplate.displayName = 'ReceiptTemplate';
