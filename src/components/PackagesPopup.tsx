import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, Crown, Zap, Code, MessageCircle, Mail, X, Copy } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface PackagesPopupProps {
  isOpen: boolean;
  onDismiss: () => void;
}

export const PackagesPopup: React.FC<PackagesPopupProps> = ({ isOpen, onDismiss }) => {
  const { toast } = useToast();

  const packages = [
    {
      id: 'monthly',
      name: 'Monthly Plan',
      price: '$10',
      period: '/month',
      icon: <Zap className="h-6 w-6 text-blue-600" />,
      features: [
        'Full access to all features',
        'Priority customer support',
        'Regular updates',
        'Cancel anytime'
      ],
      popular: false
    },
    {
      id: 'lifetime',
      name: 'Lifetime Plan',
      price: '$280',
      period: 'one-time',
      icon: <Crown className="h-6 w-6 text-yellow-600" />,
      features: [
        'Lifetime access to all features',
        'Priority customer support',
        'All future updates included',
        'Best value - Save money long-term'
      ],
      popular: true
    },
    {
      id: 'custom',
      name: 'Custom Development',
      price: 'Custom',
      period: 'pricing',
      icon: <Code className="h-6 w-6 text-purple-600" />,
      features: [
        'Tailored to your specific needs',
        'Custom features & integrations',
        'Dedicated development team',
        'Ongoing support & maintenance'
      ],
      popular: false
    }
  ];

  const handleWhatsAppContact = () => {
    const phoneNumber = '+254110860589';
    const message = encodeURIComponent('I want a business management system');
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleEmailCopy = async () => {
    const email = '<EMAIL>';
    try {
      // Try modern clipboard API first
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(email);
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = email;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }

      toast({
        title: "Email copied!",
        description: `${email} has been copied to your clipboard.`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Failed to copy email:', error);
      toast({
        variant: "destructive",
        title: "Copy failed",
        description: "Please manually copy: <EMAIL>",
        duration: 5000,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onDismiss()}>
      <DialogContent className="w-[95vw] max-w-6xl max-h-[95vh] overflow-y-auto p-4 sm:p-6">
        <DialogHeader className="relative">
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="absolute -top-2 -right-2 h-10 w-10 p-0 rounded-full bg-gray-100 hover:bg-gray-200 z-10"
          >
            <X className="h-6 w-6" />
          </Button>

          <div className="text-center mb-4">
            <div className="text-3xl sm:text-4xl font-bold text-blue-600 mb-2">VertiQ</div>
            <div className="text-sm sm:text-base text-muted-foreground">Business Management System</div>
          </div>

          <DialogTitle className="text-2xl sm:text-3xl font-bold text-center mt-4">
            Choose Your VertiQ Package
          </DialogTitle>
          <p className="text-muted-foreground text-center text-sm sm:text-base">
            Select the perfect plan for your business management needs
          </p>
        </DialogHeader>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mt-6">
          {packages.map((pkg) => (
            <Card
              key={pkg.id}
              className={`relative transition-all duration-200 hover:shadow-lg ${
                pkg.popular ? 'border-blue-500 shadow-md' : ''
              }`}
            >
              {pkg.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white text-xs sm:text-sm px-2 sm:px-3 py-1 rounded-full">
                    Most Popular
                  </span>
                </div>
              )}

              <CardHeader className="text-center p-4 sm:p-6">
                <div className="flex justify-center mb-2">
                  {pkg.icon}
                </div>
                <CardTitle className="text-lg sm:text-xl">{pkg.name}</CardTitle>
                <div className="text-2xl sm:text-3xl font-bold">
                  {pkg.price}
                  <span className="text-xs sm:text-sm font-normal text-muted-foreground block sm:inline">
                    {pkg.period}
                  </span>
                </div>
              </CardHeader>

              <CardContent className="p-4 sm:p-6 pt-0">
                <ul className="space-y-2 sm:space-y-3">
                  {pkg.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-xs sm:text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contact Section */}
        <div className="mt-6 sm:mt-8 p-4 sm:p-6 bg-gray-50 rounded-lg">
          <h3 className="text-lg sm:text-xl font-semibold text-center mb-4 sm:mb-6">
            Ready to get started? Contact us now!
          </h3>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md mx-auto">
            <Button
              onClick={handleWhatsAppContact}
              className="flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 text-sm sm:text-base py-2 sm:py-3 px-4 sm:px-6"
              size="lg"
            >
              <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5" />
              WhatsApp: +254110860589
            </Button>
            <Button
              onClick={handleEmailCopy}
              variant="outline"
              className="flex items-center justify-center gap-2 text-sm sm:text-base py-2 sm:py-3 px-4 sm:px-6 hover:bg-gray-50"
              size="lg"
              title="Click to copy email address"
            >
              <Copy className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="font-medium"><EMAIL></span>
            </Button>
          </div>
          <p className="text-xs sm:text-sm text-muted-foreground text-center mt-3 sm:mt-4">
            Get instant support and personalized recommendations
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};
