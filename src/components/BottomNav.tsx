import { useLocation, useNavigate } from "react-router-dom";
import { Package, Users, LayoutDashboard, HandCoins, LogOut, CheckSquare, ShoppingCart, Settings, User } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from 'react';
import { useMediaQuery } from "@/hooks/use-media-query";
import { useToast } from "@/components/ui/use-toast";
import { performLogout } from "@/utils/logout";

interface NavItem {
  title: string;
  url: string;
  icon: any;
  exact?: boolean;
  subItems?: Array<{
    title: string;
    url: string;
    icon?: any;
  }>;
}

const mainNavItems: NavItem[] = [
  { 
    title: "Dashboard", 
    url: "/dashboard", 
    icon: LayoutDashboard,
    exact: true
  },
  { 
    title: "Sales", 
    url: "/sales", 
    icon: HandCoins,
    subItems: [
      { title: "Overview", url: "/sales" },
      { title: "Invoices", url: "/sales/invoices" },
      { title: "Quotes", url: "/sales/quotes" },
      { title: "Revenue", url: "/sales/revenue" }
    ]
  },
  { 
    title: "Inventory", 
    url: "/inventory", 
    icon: Package,
    subItems: [
      { title: "Overview", url: "/inventory" },
      { title: "Products", url: "/inventory/products" },
      { title: "Categories", url: "/inventory/categories" },
      { title: "Stock", url: "/inventory/stock" }
    ]
  },
  { 
    title: "POS",
    url: "/pos",
    icon: ShoppingCart,
    // No subItems; simple main nav
  },
  { 
    title: "Services", 
    url: "/services", 
    icon: HandCoins,
    subItems: [
      { title: "Overview", url: "/services" },
      { title: "Service Catalog", url: "/services/catalog" },
      { title: "Appointments", url: "/services/appointments" }
    ]
  },
  {
    title: "CRM",
    url: "/crm",
    icon: Users,
    subItems: [
      { title: "Contacts", url: "/crm/contacts", icon: Users },
      { title: "Companies", url: "/crm/companies", icon: HandCoins },
      { title: "Deals", url: "/crm/deals", icon: HandCoins },
      { title: "Tasks", url: "/crm/tasks", icon: CheckSquare }
    ]
  },
  {
    title: "Profile",
    url: "/profile",
    icon: User,
    exact: true
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Settings,
    exact: true
  }
];

export function BottomNav() {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [isScrolled, setIsScrolled] = useState(false);
  const [isCRMMenuOpen, setIsCRMMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("");

  // Set active tab based on current route
  useEffect(() => {
    let currentTab = "";
    
    // First check sub-items
    for (const item of mainNavItems) {
      if (item.subItems) {
        const subItem = item.subItems.find(sub => location.pathname === sub.url);
        if (subItem) {
          currentTab = item.title;
          break;
        }
      }
      
      // Then check main items
      if (location.pathname === item.url) {
        currentTab = item.title;
        break;
      }
    }
    
    setActiveTab(currentTab);
  }, [location.pathname]);

  // Handle scroll for shadow
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = async () => {
    await performLogout({
      navigate,
      showToast: toast,
      source: 'bottom-nav'
    });
  };

  const handleNavigate = (url: string) => {
    navigate(url);
    setIsCRMMenuOpen(false);
  };

  if (!isMobile) {
    return null;
  }

  // Get CRM item and its sub-items
  const crmItem = mainNavItems.find(item => item.title === "CRM");
  const crmSubItems = crmItem?.subItems?.map(item => ({
    ...item,
    icon: item.icon || Users
  })) || [];

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      {/* Bottom Navigation */}
      <div className={cn(
        "bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700",
        "transition-shadow duration-300 w-full relative z-40",
        isScrolled ? "shadow-lg" : ""
      )} style={{ backdropFilter: 'none' }}>
        <div className="flex items-center justify-around h-16 px-1">
          {mainNavItems
            .filter(item => item.title !== "CRM")
            .map((item) => {
              const isActive = activeTab === item.title;
              const Icon = item.icon;
              
              return (
                <button
                  key={item.title}
                  onClick={() => handleNavigate(item.url)}
                  className={cn(
                    "flex-1 flex flex-col items-center justify-center h-full",
                    "text-xs font-medium transition-colors",
                    isActive ? "text-primary" : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  <div className={cn(
                    "p-2 rounded-full mb-1",
                    isActive ? "bg-primary/10" : ""
                  )}>
                    <Icon className={cn(
                      "h-5 w-5 mx-auto",
                      isActive ? "text-primary" : ""
                    )} />
                  </div>
                  <span>{item.title}</span>
                </button>
              );
            })}
          
          {/* CRM menu button */}
          <div className="relative flex-1">
            <button
              onClick={() => setIsCRMMenuOpen(!isCRMMenuOpen)}
              className={cn(
                "w-full flex flex-col items-center justify-center h-full",
                "text-xs font-medium transition-colors",
                isCRMMenuOpen ? "text-primary" : "text-muted-foreground hover:text-foreground"
              )}
            >
              <div className={cn(
                "p-2 rounded-full mb-1",
                isCRMMenuOpen ? "bg-primary/10" : ""
              )}>
                <Users className={cn(
                  "h-5 w-5 mx-auto",
                  isCRMMenuOpen ? "text-primary" : ""
                )} />
              </div>
              <span>CRM</span>
            </button>

            {/* CRM Submenu */}
            <AnimatePresence mode="wait">
              {isCRMMenuOpen && crmSubItems.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  transition={{ type: "spring", damping: 20, stiffness: 300 }}
                  className="fixed bottom-16 left-1/2 -translate-x-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden z-[60] min-w-[180px] transform origin-bottom"
                  onClick={(e) => e.stopPropagation()}
                  style={{ willChange: 'transform, opacity' }}
                >
                  <div className="py-1">
                    {crmSubItems.map((subItem) => {
                      const Icon = subItem.icon || Users;
                      return (
                        <button
                          key={subItem.url}
                          onClick={() => handleNavigate(subItem.url)}
                          className={cn(
                            "w-full flex items-center px-4 py-3 text-sm text-left",
                            "transition-colors hover:bg-accent/50",
                            location.pathname === subItem.url
                              ? "bg-primary/10 text-primary font-medium"
                              : "text-foreground"
                          )}
                        >
                          <Icon className="h-4 w-4 mr-3 opacity-80" />
                          {subItem.title}
                        </button>
                      );
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Backdrop */}
      <AnimatePresence>
        {isCRMMenuOpen && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-transparent"
            onClick={() => setIsCRMMenuOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
}
