
import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Camera, Save, Upload, User, Building2, Globe } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useUserSettings } from "@/hooks/useUserSettings";
import { useSubscriptionContext } from "@/components/SubscriptionProvider";
import { supabase } from "@/integrations/supabase/client";

export default function Profile() {
  const { user } = useSubscriptionContext();
  const { settings, loading, updateSettings, refetch } = useUserSettings(user);
  const [uploading, setUploading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const { toast } = useToast();

  const [profile, setProfile] = useState({
    display_name: '',
    email: '',
    company_name: '',
    avatar_url: '',
    language: 'en',
    timezone: 'UTC',
    currency: 'USD',
    user_currency: 'KES',
    country: ''
  });

  const currencies = [
    { value: 'KES', label: 'Kenyan Shilling (KES)' },
    { value: 'USD', label: 'US Dollar (USD)' },
    { value: 'EUR', label: 'Euro (EUR)' },
    { value: 'GBP', label: 'British Pound (GBP)' },
    { value: 'ZAR', label: 'South African Rand (ZAR)' },
    { value: 'NGN', label: 'Nigerian Naira (NGN)' },
    { value: 'GHS', label: 'Ghanaian Cedi (GHS)' },
    { value: 'UGX', label: 'Ugandan Shilling (UGX)' },
    { value: 'TZS', label: 'Tanzanian Shilling (TZS)' },
    { value: 'RWF', label: 'Rwandan Franc (RWF)' },
    { value: 'ETB', label: 'Ethiopian Birr (ETB)' },
    { value: 'MAD', label: 'Moroccan Dirham (MAD)' },
    { value: 'EGP', label: 'Egyptian Pound (EGP)' }
  ];

  useEffect(() => {
    if (settings) {
      const newProfile = {
        display_name: settings.display_name || '',
        email: settings.email || user?.email || '',
        company_name: settings.company_name || '',
        avatar_url: settings.avatar_url || '',
        language: settings.language || 'en',
        timezone: settings.timezone || 'UTC',
        currency: settings.currency || 'USD',
        user_currency: settings.user_currency || 'KES',
        country: settings.country || ''
      };
      setProfile(newProfile);
      setHasChanges(false); // Reset changes flag when loading fresh data
    } else if (user) {
      setProfile(prev => ({
        ...prev,
        email: user.email || ''
      }));
    }
  }, [settings, user]);

  // Track changes to profile data
  useEffect(() => {
    if (settings) {
      const hasProfileChanges =
        profile.display_name !== (settings.display_name || '') ||
        profile.email !== (settings.email || user?.email || '') ||
        profile.company_name !== (settings.company_name || '') ||
        profile.avatar_url !== (settings.avatar_url || '') ||
        profile.language !== (settings.language || 'en') ||
        profile.timezone !== (settings.timezone || 'UTC') ||
        profile.currency !== (settings.currency || 'USD') ||
        profile.user_currency !== (settings.user_currency || 'KES') ||
        profile.country !== (settings.country || '');

      setHasChanges(hasProfileChanges);
    }
  }, [profile, settings, user]);

  const uploadAvatar = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setUploading(true);
      const file = event.target.files?.[0];
      if (!file || !user) return;

      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}.${fileExt}`;
      const filePath = `${user.id}/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, { upsert: true });

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      setProfile(prev => ({
        ...prev,
        avatar_url: data.publicUrl
      }));

      toast({
        title: 'Success',
        description: 'Logo uploaded successfully',
      });
    } catch (error) {
      console.error('Error uploading logo:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to upload logo',
      });
    } finally {
      setUploading(false);
    }
  };

  const saveProfile = async () => {
    try {
      setSaving(true);
      const updatedSettings = await updateSettings({
        display_name: profile.display_name,
        email: profile.email,
        company_name: profile.company_name,
        avatar_url: profile.avatar_url,
        language: profile.language,
        timezone: profile.timezone,
        currency: profile.currency,
        user_currency: profile.user_currency,
        country: profile.country
      });

      // Ensure the local state reflects the saved data
      if (updatedSettings) {
        setProfile({
          display_name: updatedSettings.display_name || '',
          email: updatedSettings.email || user?.email || '',
          company_name: updatedSettings.company_name || '',
          avatar_url: updatedSettings.avatar_url || '',
          language: updatedSettings.language || 'en',
          timezone: updatedSettings.timezone || 'UTC',
          currency: updatedSettings.currency || 'USD',
          user_currency: updatedSettings.user_currency || 'KES',
          country: updatedSettings.country || ''
        });

        // Also refresh the settings from the database to ensure consistency
        await refetch();
        setHasChanges(false); // Reset changes flag after successful save
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save profile changes. Please try again.',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Profile</h1>
        <Card>
          <CardHeader className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </CardHeader>
          <CardContent className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Profile Settings</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Logo and Basic Info */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Logo
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col items-center space-y-4">
              <Avatar className="h-32 w-32">
                <AvatarImage src={profile.avatar_url} alt="Logo" />
                <AvatarFallback className="text-2xl">
                  {profile.display_name?.split(' ').map(n => n[0]).join('') || 'L'}
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <Label htmlFor="avatar-upload" className="cursor-pointer">
                  <Button variant="outline" disabled={uploading} asChild>
                    <span>
                      {uploading ? (
                        <Upload className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Camera className="mr-2 h-4 w-4" />
                      )}
                      {uploading ? 'Uploading...' : 'Change Logo'}
                    </span>
                  </Button>
                </Label>
                <input
                  id="avatar-upload"
                  type="file"
                  accept="image/*"
                  onChange={uploadAvatar}
                  className="hidden"
                />
                <p className="text-sm text-muted-foreground mt-2">
                  JPG, PNG or GIF. Max size 2MB.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="display_name">Display Name</Label>
                <Input
                  id="display_name"
                  value={profile.display_name}
                  onChange={(e) => setProfile(prev => ({ ...prev, display_name: e.target.value }))}
                  placeholder="Enter your display name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={profile.email}
                  onChange={(e) => setProfile(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="Enter your email"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  value={profile.country}
                  onChange={(e) => setProfile(prev => ({ ...prev, country: e.target.value }))}
                  placeholder="Enter your country"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Company Information */}
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Company Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="company_name">Company Name</Label>
                <Input
                  id="company_name"
                  value={profile.company_name}
                  onChange={(e) => setProfile(prev => ({ ...prev, company_name: e.target.value }))}
                  placeholder="Enter your company name"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preferences */}
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Preferences
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-4">
              <div className="space-y-2">
                <Label htmlFor="user_currency">Preferred Currency</Label>
                <Select 
                  value={profile.user_currency} 
                  onValueChange={(value) => setProfile(prev => ({ ...prev, user_currency: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">Language</Label>
                <Input
                  id="language"
                  value={profile.language}
                  onChange={(e) => setProfile(prev => ({ ...prev, language: e.target.value }))}
                  placeholder="en"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="timezone">Timezone</Label>
                <Input
                  id="timezone"
                  value={profile.timezone}
                  onChange={(e) => setProfile(prev => ({ ...prev, timezone: e.target.value }))}
                  placeholder="UTC"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency">System Currency</Label>
                <Input
                  id="currency"
                  value={profile.currency}
                  onChange={(e) => setProfile(prev => ({ ...prev, currency: e.target.value }))}
                  placeholder="USD"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end">
        <Button
          onClick={saveProfile}
          disabled={loading || saving || !hasChanges}
          size="lg"
          className={hasChanges ? 'bg-blue-600 hover:bg-blue-700' : ''}
        >
          <Save className="mr-2 h-4 w-4" />
          {saving ? 'Saving...' : hasChanges ? 'Save Changes' : 'No Changes'}
        </Button>
        {hasChanges && (
          <p className="text-sm text-amber-600 mt-2">
            You have unsaved changes
          </p>
        )}
      </div>
    </div>
  );
}
