import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Plus } from "lucide-react";
import { AddExpenseForm } from "@/components/forms/AddExpenseForm";
import { AddSaleForm } from "@/components/forms/AddSaleForm";

interface Sale {
  id: number;
  customerId: string;
  customerName: string;
  itemName: string;
  itemType: 'product' | 'service';
  amount: number;
  unitPrice: number;
  quantity: number;
  paymentMethod: string;
  date: string;
  status: string;
  notes?: string;
  priceOverridden?: boolean;
}

interface Product {
  id: string;
  name: string;
  price: number;
  type: 'product';
  stock_quantity: number;
  description?: string;
}

interface Service {
  id: string;
  name: string;
  price: number;
  type: 'service';
  description?: string;
}

interface Contact {
  id: string;
  name: string;
  email?: string;
  phone?: string;
}

interface Expense {
  id: number;
  description: string;
  category: string;
  amount: number;
  date: string;
  status: 'pending' | 'completed' | 'rejected';
  notes?: string;
}

export function SalesPage() {
  const [showSaleForm, setShowSaleForm] = useState(false);
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [sales, setSales] = useState<Sale[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);

  // Sample data for the form
  const [products] = useState<Product[]>([
    { id: '1', name: 'Product 1', price: 100, type: 'product', stock_quantity: 10 },
    { id: '2', name: 'Product 2', price: 200, type: 'product', stock_quantity: 5 },
  ]);

  const [services] = useState<Service[]>([
    { id: 's1', name: 'Service 1', price: 150, type: 'service' },
    { id: 's2', name: 'Service 2', price: 250, type: 'service' },
  ]);

  const [contacts] = useState<Contact[]>([
    { id: 'c1', name: 'John Doe', email: '<EMAIL>', phone: '1234567890' },
    { id: 'c2', name: 'Jane Smith', email: '<EMAIL>', phone: '0987654321' },
  ]);

  const handleAddSale = (saleData: any) => {
    const customer = contacts.find(c => c.id === saleData.customerId);
    
    const newSale: Sale = {
      id: Date.now(),
      customerId: saleData.customerId,
      customerName: customer?.name || 'Unknown',
      itemName: saleData.itemName,
      itemType: saleData.itemType,
      amount: saleData.amount,
      unitPrice: saleData.unitPrice,
      quantity: saleData.quantity || 1,
      paymentMethod: saleData.paymentMethod,
      date: saleData.date,
      status: 'completed',
      notes: saleData.notes,
      priceOverridden: saleData.priceOverridden
    };
    
    setSales([...sales, newSale]);
    setShowSaleForm(false);
  };

  const handleAddExpense = (expense: Omit<Expense, 'id' | 'status'>) => {
    const newExpense: Expense = {
      ...expense,
      id: Date.now(),
      status: 'pending',
      category: expense.category || 'uncategorized'
    };
    setExpenses([...expenses, newExpense]);
    setShowExpenseForm(false);
  };

  // Calculate totals
  const totalSales = sales.reduce((sum, sale) => sum + sale.amount, 0);
  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
  const profit = totalSales - totalExpenses;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Sales & Expenses</h1>
        <div className="space-x-2">
          <Button onClick={() => setShowSaleForm(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Sale
          </Button>
          <Button variant="outline" onClick={() => setShowExpenseForm(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Expense
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <div className="rounded-lg border p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Total Sales</h3>
          <p className="text-2xl font-bold">${totalSales.toFixed(2)}</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Total Expenses</h3>
          <p className="text-2xl font-bold">${totalExpenses.toFixed(2)}</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Profit</h3>
          <p className={`text-2xl font-bold ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            ${Math.abs(profit).toFixed(2)} {profit >= 0 ? 'Profit' : 'Loss'}
          </p>
        </div>
      </div>

      <Tabs defaultValue="sales" className="space-y-4">
        <TabsList>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="expenses">Expenses</TabsTrigger>
        </TabsList>

        <TabsContent value="sales">
          <div className="rounded-md border">
            {sales.length === 0 ? (
              <div className="p-8 text-center text-muted-foreground">
                No sales recorded yet.
              </div>
            ) : (
              <div className="divide-y">
                {sales.map((sale) => (
                  <div key={sale.id} className="p-4 flex justify-between items-center">
                    <div>
                      <p className="font-medium">{sale.customerName}</p>
                      <p className="text-sm text-muted-foreground">
                        {sale.quantity > 1 ? `${sale.quantity}x ` : ''}{sale.itemName}
                        {sale.priceOverridden && ' (Price Adjusted)'}
                      </p>
                      {sale.notes && (
                        <p className="text-xs text-muted-foreground mt-1">{sale.notes}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${sale.amount.toFixed(2)}</p>
                      <p className="text-sm text-muted-foreground capitalize">
                        {sale.paymentMethod} • {new Date(sale.date).toLocaleDateString()}
                        {sale.quantity > 1 && ` • $${sale.unitPrice.toFixed(2)} each`}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="expenses">
          <div className="rounded-md border">
            {expenses.length === 0 ? (
              <div className="p-8 text-center text-muted-foreground">
                No expenses recorded yet.
              </div>
            ) : (
              <div className="divide-y">
                {expenses.map((expense) => (
                  <div key={expense.id} className="p-4 flex justify-between items-center">
                    <div>
                      <p className="font-medium">{expense.description}</p>
                      <p className="text-sm text-muted-foreground capitalize">
                        {expense.category.replace('-', ' ')}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">-${expense.amount.toFixed(2)}</p>
                      <p className="text-sm text-muted-foreground">
                        {expense.date}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Add Sale Form */}
      {showSaleForm && (
        <AddSaleForm
          onClose={() => setShowSaleForm(false)}
          onSubmit={handleAddSale}
          products={products}
          services={services}
          contacts={contacts}
        />
      )}

      {showExpenseForm && (
        <AddExpenseForm
          onClose={() => setShowExpenseForm(false)}
          onSubmit={handleAddExpense}
        />
      )}
    </div>
  );
}
