
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUserSettings } from "@/hooks/useUserSettings";
import { useSubscriptionContext } from "@/components/SubscriptionProvider";
import { getAllCurrencies } from "@/utils/currency";
import { SettingsValidation } from "@/components/SettingsValidation";
import { useOnboarding } from "@/components/onboarding/OnboardingProvider";
import { HelpCircle, Play } from "lucide-react";

const Settings = () => {
  const { user } = useSubscriptionContext();
  const { settings, loading, updateSettings } = useUserSettings(user);
  const { startOnboarding } = useOnboarding();
  const [formData, setFormData] = useState({
    company_name: '',
    timezone: 'UTC',
    currency: 'KES',
    user_currency: 'KES',
    display_name: '',
    email: '',
    language: 'en'
  });

  useEffect(() => {
    if (settings) {
      setFormData({
        company_name: settings.company_name || '',
        timezone: settings.timezone || 'UTC',
        currency: settings.currency || 'KES',
        user_currency: settings.user_currency || 'KES',
        display_name: settings.display_name || '',
        email: settings.email || '',
        language: settings.language || 'en'
      });
    }
  }, [settings]);

  const handleSaveGeneral = async () => {
    try {
      const updatedSettings = await updateSettings({
        company_name: formData.company_name,
        timezone: formData.timezone,
        currency: formData.user_currency,
        user_currency: formData.user_currency
      });

      // Update local form data to reflect saved changes
      if (updatedSettings) {
        setFormData(prev => ({
          ...prev,
          company_name: updatedSettings.company_name || '',
          timezone: updatedSettings.timezone || 'UTC',
          user_currency: updatedSettings.user_currency || 'KES'
        }));
      }
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleSaveProfile = async () => {
    try {
      const updatedSettings = await updateSettings({
        display_name: formData.display_name,
        email: formData.email,
        language: formData.language
      });

      // Update local form data to reflect saved changes
      if (updatedSettings) {
        setFormData(prev => ({
          ...prev,
          display_name: updatedSettings.display_name || '',
          email: updatedSettings.email || '',
          language: updatedSettings.language || 'en'
        }));
      }
    } catch (error) {
      // Error handled by hook
    }
  };

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <h1 className="text-2xl font-bold text-gray-800">Settings</h1>
        <div className="animate-pulse space-y-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="border p-6 rounded-lg bg-white shadow-sm">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-bold text-gray-800">Settings</h1>

      {/* Settings Validation Status */}
      {settings && (
        <div className="mb-6">
          <SettingsValidation
            settings={{
              company_name: settings.company_name,
              avatar_url: settings.avatar_url,
              display_name: settings.display_name,
              email: settings.email,
              currency: settings.user_currency || settings.currency,
              country: settings.country,
            }}
            showCard={true}
          />
        </div>
      )}

      <div className="space-y-6">
        {/* General Settings */}
        <div className="border p-6 rounded-lg bg-white shadow-sm">
          <h2 className="text-xl font-semibold mb-4">General Settings</h2>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Company Name</Label>
              <Input 
                placeholder="Enter company name" 
                value={formData.company_name}
                onChange={(e) => setFormData({...formData, company_name: e.target.value})}
              />
            </div>
            
            <div className="space-y-2">
              <Label>Timezone</Label>
              <Select 
                value={formData.timezone}
                onValueChange={(value) => setFormData({...formData, timezone: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UTC">UTC</SelectItem>
                  <SelectItem value="EST">EST</SelectItem>
                  <SelectItem value="PST">PST</SelectItem>
                  <SelectItem value="GMT">GMT</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Preferred Currency</Label>
              <Select
                value={formData.user_currency}
                onValueChange={(value) => setFormData({...formData, user_currency: value, currency: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {getAllCurrencies().map((currency) => (
                    <SelectItem key={currency.value} value={currency.value}>
                      {currency.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button onClick={handleSaveGeneral}>Save General Settings</Button>
          </div>
        </div>

        {/* Profile Settings */}
        <div className="border p-6 rounded-lg bg-white shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Profile Settings</h2>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Display Name</Label>
              <Input 
                placeholder="Enter display name" 
                value={formData.display_name}
                onChange={(e) => setFormData({...formData, display_name: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label>Email</Label>
              <Input 
                type="email" 
                placeholder="Enter email" 
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label>Language</Label>
              <Select 
                value={formData.language}
                onValueChange={(value) => setFormData({...formData, language: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="es">Spanish</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button onClick={handleSaveProfile}>Save Profile Settings</Button>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="border p-6 rounded-lg bg-white shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Notification Settings</h2>
          <div className="space-y-4">
            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">Email Notifications</Label>
                <p className="text-sm text-gray-500">Receive notifications via email</p>
              </div>
              <Switch />
            </div>

            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">Sales Alerts</Label>
                <p className="text-sm text-gray-500">Get notified about new sales</p>
              </div>
              <Switch />
            </div>

            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">Inventory Alerts</Label>
                <p className="text-sm text-gray-500">Get notified about low stock</p>
              </div>
              <Switch />
            </div>

            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">Marketing Updates</Label>
                <p className="text-sm text-gray-500">Receive marketing newsletters</p>
              </div>
              <Switch />
            </div>

            <Button>Save Notification Settings</Button>
          </div>
        </div>

        {/* Help & Support */}
        <div className="border p-6 rounded-lg bg-white shadow-sm">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Help & Support
          </h2>
          <div className="space-y-4">
            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">Onboarding Tour</Label>
                <p className="text-sm text-gray-500">
                  Restart the guided tour to learn how to use the system
                </p>
              </div>
              <Button
                variant="outline"
                onClick={startOnboarding}
                className="gap-2"
              >
                <Play className="h-4 w-4" />
                Start Tour
              </Button>
            </div>

            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">User Guide</Label>
                <p className="text-sm text-gray-500">
                  Access the complete user documentation
                </p>
              </div>
              <Button
                variant="outline"
                onClick={() => window.open('/USER_GUIDE.md', '_blank')}
              >
                View Guide
              </Button>
            </div>

            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">Contact Support</Label>
                <p className="text-sm text-gray-500">
                  Get help from our support team
                </p>
              </div>
              <Button
                variant="outline"
                onClick={() => window.open('/contact', '_blank')}
              >
                Contact Us
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
