
import { Card, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { TrendingDown, TrendingUp, AlertTriangle, Calendar, DollarSign, Target } from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, Area, AreaChart } from 'recharts'
import { useCurrency } from "@/contexts/CurrencyContext";
import { formatCurrency } from "@/utils/currency";

// Mock data for runway calculations
const runwayData = {
  currentCash: 125000,
  monthlyBurnRate: 18500,
  monthlyRevenue: 12000,
  netBurnRate: 6500, // monthlyBurnRate - monthlyRevenue
  projectedRunway: 19.2, // months
  lastUpdated: new Date().toISOString().split('T')[0]
}

// Monthly projection data
const monthlyProjections = [
  { month: 'Jan 2024', cash: 125000, revenue: 12000, expenses: 18500, runway: 19.2 },
  { month: 'Feb 2024', cash: 118500, revenue: 12000, expenses: 18500, runway: 18.2 },
  { month: 'Mar 2024', cash: 112000, revenue: 12000, expenses: 18500, runway: 17.2 },
  { month: 'Apr 2024', cash: 105500, revenue: 12000, expenses: 18500, runway: 16.2 },
  { month: 'May 2024', cash: 99000, revenue: 12000, expenses: 18500, runway: 15.2 },
  { month: 'Jun 2024', cash: 92500, revenue: 12000, expenses: 18500, runway: 14.2 },
  { month: 'Jul 2024', cash: 86000, revenue: 12000, expenses: 18500, runway: 13.2 },
  { month: 'Aug 2024', cash: 79500, revenue: 12000, expenses: 18500, runway: 12.2 },
  { month: 'Sep 2024', cash: 73000, revenue: 12000, expenses: 18500, runway: 11.2 },
  { month: 'Oct 2024', cash: 66500, revenue: 12000, expenses: 18500, runway: 10.2 },
  { month: 'Nov 2024', cash: 60000, revenue: 12000, expenses: 18500, runway: 9.2 },
  { month: 'Dec 2024', cash: 53500, revenue: 12000, expenses: 18500, runway: 8.2 },
]

// Scenario analysis data
const scenarios = [
  {
    name: 'Current Trajectory',
    monthlyRevenue: 12000,
    monthlyExpenses: 18500,
    runway: 19.2,
    color: '#ef4444',
    description: 'Based on current revenue and expense trends'
  },
  {
    name: 'Conservative Growth',
    monthlyRevenue: 15000,
    monthlyExpenses: 18500,
    runway: 35.7,
    color: '#f59e0b',
    description: '25% revenue increase, same expenses'
  },
  {
    name: 'Aggressive Growth',
    monthlyRevenue: 20000,
    monthlyExpenses: 18500,
    runway: 'Profitable',
    color: '#10b981',
    description: '67% revenue increase, same expenses'
  },
  {
    name: 'Cost Optimization',
    monthlyRevenue: 12000,
    monthlyExpenses: 15000,
    runway: 41.7,
    color: '#3b82f6',
    description: 'Current revenue, 19% expense reduction'
  }
]

// Remove the local formatCurrency function since we're using the global one

const getRunwayStatus = (months: number) => {
  if (months < 6) return { status: 'critical', color: 'bg-red-500', textColor: 'text-red-600' }
  if (months < 12) return { status: 'warning', color: 'bg-yellow-500', textColor: 'text-yellow-600' }
  return { status: 'healthy', color: 'bg-green-500', textColor: 'text-green-600' }
}

export default function FinancialRunway() {
  const { currency } = useCurrency();
  const runwayStatus = getRunwayStatus(runwayData.projectedRunway)
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Financial Runway</h1>
          <p className="text-sm text-muted-foreground">
            Monitor your business cash flow and financial sustainability
          </p>
        </div>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Calendar className="h-4 w-4" />
          Last updated: {new Date(runwayData.lastUpdated).toLocaleDateString()}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Runway</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{runwayData.projectedRunway} months</div>
            <div className="flex items-center space-x-2 mt-2">
              <div className={`h-2 w-2 rounded-full ${runwayStatus.color}`} />
              <p className={`text-xs ${runwayStatus.textColor} capitalize`}>
                {runwayStatus.status}
              </p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Cash</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(runwayData.currentCash, currency || 'KES')}</div>
            <p className="text-xs text-muted-foreground">Available funds</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Burn Rate</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{formatCurrency(runwayData.netBurnRate, currency || 'KES')}</div>
            <p className="text-xs text-muted-foreground">Net monthly outflow</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(runwayData.monthlyRevenue, currency || 'KES')}</div>
            <p className="text-xs text-muted-foreground">Average monthly income</p>
          </CardContent>
        </Card>
      </div>

      {/* Runway Alert */}
      {runwayData.projectedRunway < 12 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
              <CardTitle className="text-amber-900">Runway Alert</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-amber-800">
              Your current financial runway is {runwayData.projectedRunway} months. 
              {runwayData.projectedRunway < 6 ? 
                ' This is critically low and requires immediate attention.' :
                ' Consider implementing cost reduction measures or increasing revenue.'
              }
            </p>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Cash Flow Projection */}
        <Card>
          <CardHeader>
            <CardTitle>Cash Flow Projection</CardTitle>
            <CardDescription>12-month cash flow forecast based on current trends</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={monthlyProjections}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                />
                <Tooltip 
                  formatter={(value, name) => [formatCurrency(Number(value), currency || 'KES'), name]}
                  labelFormatter={(label) => `Month: ${label}`}
                />
                <Area 
                  type="monotone" 
                  dataKey="cash" 
                  stroke="#3b82f6" 
                  fill="#3b82f6" 
                  fillOpacity={0.3}
                  name="Cash Balance"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Monthly Revenue vs Expenses */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue vs Expenses</CardTitle>
            <CardDescription>Monthly comparison of income and expenses</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyProjections}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                />
                <Tooltip 
                  formatter={(value, name) => [formatCurrency(Number(value), currency || 'KES'), name]}
                  labelFormatter={(label) => `Month: ${label}`}
                />
                <Bar dataKey="revenue" fill="#10b981" name="Revenue" />
                <Bar dataKey="expenses" fill="#ef4444" name="Expenses" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Scenario Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Scenario Analysis</CardTitle>
          <CardDescription>How different scenarios would affect your financial runway</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {scenarios.map((scenario, index) => (
              <div key={index} className="p-4 border rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">{scenario.name}</h3>
                  <Badge 
                    variant={scenario.runway === 'Profitable' ? 'default' : 
                            typeof scenario.runway === 'number' && scenario.runway > 24 ? 'default' : 'secondary'}
                    style={{ backgroundColor: scenario.color }}
                  >
                    {scenario.runway === 'Profitable' ? 'Profitable' : `${scenario.runway} months`}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{scenario.description}</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Revenue: </span>
                    <span className="font-medium text-green-600">{formatCurrency(scenario.monthlyRevenue, currency || 'KES')}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Expenses: </span>
                    <span className="font-medium text-red-600">{formatCurrency(scenario.monthlyExpenses, currency || 'KES')}</span>
                  </div>
                </div>
                {scenario.runway !== 'Profitable' && typeof scenario.runway === 'number' && (
                  <Progress 
                    value={Math.min((scenario.runway / 36) * 100, 100)} 
                    className="h-2"
                  />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Action Items */}
      <Card>
        <CardHeader>
          <CardTitle>Recommended Actions</CardTitle>
          <CardDescription>Steps to improve your financial runway</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {runwayData.projectedRunway < 6 && (
              <div className="flex items-start space-x-3 p-3 bg-red-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-red-900">Immediate Action Required</h4>
                  <p className="text-sm text-red-700 mt-1">
                    Your runway is critically low. Consider emergency cost cuts, accelerated sales efforts, or securing additional funding immediately.
                  </p>
                </div>
              </div>
            )}
            
            {runwayData.projectedRunway < 12 && runwayData.projectedRunway >= 6 && (
              <div className="flex items-start space-x-3 p-3 bg-amber-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-amber-900">Action Recommended</h4>
                  <p className="text-sm text-amber-700 mt-1">
                    Your runway could be improved. Focus on increasing revenue or reducing non-essential expenses.
                  </p>
                </div>
              </div>
            )}

            <div className="grid gap-3 md:grid-cols-2">
              <div className="p-3 border rounded-lg">
                <h4 className="font-medium text-green-900">Increase Revenue</h4>
                <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                  <li>• Launch new product lines</li>
                  <li>• Improve sales conversion rates</li>
                  <li>• Implement upselling strategies</li>
                  <li>• Expand to new markets</li>
                </ul>
              </div>
              
              <div className="p-3 border rounded-lg">
                <h4 className="font-medium text-blue-900">Reduce Expenses</h4>
                <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                  <li>• Review and cut unnecessary subscriptions</li>
                  <li>• Negotiate better rates with suppliers</li>
                  <li>• Optimize operational efficiency</li>
                  <li>• Consider remote work options</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
