
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, FileText, DollarSign, Clock, CheckCircle, Eye, Printer, Download, MoreVertical, Trash2 } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { CreateInvoiceForm } from "@/components/forms/CreateInvoiceForm";
import { InvoiceViewer } from "@/components/InvoiceViewer";
import { useInvoices } from "@/hooks/useInvoices";
import { useCurrency } from "@/contexts/CurrencyContext";
import { formatCurrency } from "@/utils/currency";
import { format } from "date-fns";

export default function Invoices() {
  const { invoices, loading, addInvoice, deleteInvoice } = useInvoices();
  const { currency } = useCurrency();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [showInvoiceViewer, setShowInvoiceViewer] = useState(false);

  const handleCreateInvoice = async (newInvoice: any) => {
    try {
      await addInvoice(newInvoice);
      setShowCreateForm(false);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleViewInvoice = (invoice: any) => {
    setSelectedInvoice(invoice);
    setShowInvoiceViewer(true);
  };

  const handleDeleteInvoice = async (invoiceId: string) => {
    if (window.confirm('Are you sure you want to delete this invoice?')) {
      try {
        await deleteInvoice(invoiceId);
      } catch (error) {
        // Error handled by hook
      }
    }
  };

  const totalAmount = invoices.reduce((sum, invoice) => sum + Number(invoice.amount), 0);
  const paidInvoices = invoices.filter(inv => inv.status === "paid").length;
  const overdueInvoices = invoices.filter(inv => inv.status === "overdue").length;
  const draftInvoices = invoices.filter(inv => inv.status === "draft").length;

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Invoices</h1>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-4 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
            Invoice Management
          </h1>
          <p className="text-muted-foreground mt-1">Create, manage, and track your invoices</p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          size="lg"
          className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white shadow-lg"
        >
          <Plus className="mr-2 h-5 w-5" /> Create Invoice
        </Button>
      </div>
      
      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Total Invoices</CardTitle>
            <div className="p-2 bg-blue-200 rounded-lg">
              <FileText className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-700">{invoices.length}</div>
            <p className="text-xs text-blue-600 mt-1">All invoices</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Total Amount</CardTitle>
            <div className="p-2 bg-green-200 rounded-lg">
              <DollarSign className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-700">
              {formatCurrency(totalAmount, currency || 'KES')}
            </div>
            <p className="text-xs text-green-600 mt-1">All invoices</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-emerald-700">Paid</CardTitle>
            <div className="p-2 bg-emerald-200 rounded-lg">
              <CheckCircle className="h-4 w-4 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-emerald-700">{paidInvoices}</div>
            <p className="text-xs text-emerald-600 mt-1">Completed payments</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-amber-700">Overdue</CardTitle>
            <div className="p-2 bg-amber-200 rounded-lg">
              <Clock className="h-4 w-4 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-amber-700">{overdueInvoices}</div>
            <p className="text-xs text-amber-600 mt-1">Need attention</p>
          </CardContent>
        </Card>
      </div>
      
      {/* Invoices List */}
      <Card className="shadow-lg border-0">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-600" />
            Invoices
            <Badge className="bg-green-100 text-green-700">
              {invoices.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {invoices.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground text-lg">No invoices created yet</p>
              <p className="text-sm text-muted-foreground">Create your first invoice to get started</p>
            </div>
          ) : (
            <div className="space-y-4">
              {invoices.map((invoice) => (
                <Card key={invoice.id} className="border-2 hover:border-green-300 transition-all duration-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">#{invoice.invoice_number}</h3>
                          <Badge
                            variant={
                              invoice.status === 'paid' ? 'default' :
                              invoice.status === 'overdue' ? 'destructive' :
                              'secondary'
                            }
                            className={
                              invoice.status === 'paid' ? 'bg-green-500 text-white' :
                              invoice.status === 'overdue' ? 'bg-red-500 text-white' :
                              'bg-yellow-500 text-white'
                            }
                          >
                            {invoice.status === 'paid' && <CheckCircle className="h-3 w-3 mr-1" />}
                            {invoice.status === 'overdue' && <Clock className="h-3 w-3 mr-1" />}
                            {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground mb-1">Customer: {invoice.customer}</p>
                        <div className="flex gap-4 text-sm text-muted-foreground">
                          <span>Due: {format(new Date(invoice.due_date), 'MMM dd, yyyy')}</span>
                          <span>Created: {format(new Date(invoice.created_at), 'MMM dd, yyyy')}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="text-2xl font-bold text-green-600">
                            {formatCurrency(Number(invoice.amount), currency || 'KES')}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewInvoice(invoice)}
                            className="flex items-center gap-1"
                          >
                            <Eye className="h-4 w-4" />
                            View
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleViewInvoice(invoice)}>
                                <Eye className="mr-2 h-4 w-4" />
                                <span>View & Print</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => handleDeleteInvoice(invoice.id)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Invoice Form */}
      {showCreateForm && (
        <CreateInvoiceForm
          onClose={() => setShowCreateForm(false)}
          onSubmit={handleCreateInvoice}
        />
      )}

      {/* Invoice Viewer */}
      {selectedInvoice && (
        <InvoiceViewer
          invoice={selectedInvoice}
          isOpen={showInvoiceViewer}
          onClose={() => {
            setShowInvoiceViewer(false);
            setSelectedInvoice(null);
          }}
        />
      )}
    </div>
  );
}
