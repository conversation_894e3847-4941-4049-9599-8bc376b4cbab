
import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Wrench, DollarSign, TrendingUp, Target, Pencil, Trash2, MoreVertical, BarChart3, Search, Filter, Zap, CheckCircle, AlertTriangle, Grid, List } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { useServices } from "@/hooks/useServices";
import { useReceipts } from "@/hooks/useReceipts";
import { useContacts } from "@/hooks/useContacts";
import { useCurrency } from "@/contexts/CurrencyContext";
import { formatCurrency } from "@/utils/currency";
import { convertServiceSaleToReceipt } from "@/utils/salesToReceipt";
import { useToast } from "@/components/ui/use-toast";

interface ServiceFormData {
  name: string;
  category: string;
  description: string;
  price: number;
  status: 'active' | 'inactive';
}

export default function Services() {
  const { services, loading, addService, updateService, deleteService } = useServices();
  const { addReceipt } = useReceipts();
  const { contacts } = useContacts();
  const { currency } = useCurrency();
  const { toast } = useToast();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingService, setEditingService] = useState<ServiceFormData & { id?: string } | null>(null);
  const [sellServiceDialog, setSellServiceDialog] = useState<{ open: boolean; service: any | null }>({ open: false, service: null });
  const [saleForm, setSaleForm] = useState({ customer: '', quantity: 1, notes: '' });
  const [newService, setNewService] = useState<ServiceFormData>({
    name: '',
    category: '',
    description: '',
    price: 0,
    status: 'active' as const,
  });

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (editingService) {
      setEditingService(prev => ({
        ...prev!,
        [name]: name === 'price' ? parseFloat(value) || 0 : value
      }));
    } else {
      setNewService(prevState => ({
        ...prevState,
        [name]: name === 'price' ? parseFloat(value) || 0 : value
      }));
    }
  };

  const handleStatusChange = (value: string) => {
    if (value !== 'active' && value !== 'inactive') return;
    
    if (editingService) {
      setEditingService(prev => ({
        ...prev!,
        status: value as 'active' | 'inactive'
      }));
    } else {
      setNewService(prev => ({
        ...prev,
        status: value as 'active' | 'inactive'
      }));
    }
  };

  const handleAddService = async () => {
    if (!newService.name || !newService.category) {
      return;
    }

    try {
      await addService({
        name: newService.name,
        category: newService.category,
        description: newService.description,
        price: newService.price,
        status: newService.status,
      });
      setNewService({
        name: '',
        category: '',
        description: '',
        price: 0,
        status: 'active',
      });
      setIsFormOpen(false);
    } catch (error) {
      console.error('Error adding service:', error);
    }
  };

  const handleEditService = (service: any) => {
    setEditingService({
      id: service.id,
      name: service.name,
      category: service.category,
      description: service.description,
      price: service.price,
      status: service.status,
    });
    setIsFormOpen(true);
  };

  const handleUpdateService = async () => {
    if (!editingService?.id || !editingService.name || !editingService.category) {
      return;
    }

    try {
      await updateService(editingService.id, {
        name: editingService.name,
        category: editingService.category,
        description: editingService.description,
        price: editingService.price,
        status: editingService.status,
      });
      setEditingService(null);
      setIsFormOpen(false);
    } catch (error) {
      console.error('Error updating service:', error);
    }
  };

  const handleDeleteService = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      try {
        await deleteService(id);
      } catch (error) {
        console.error('Error deleting service:', error);
      }
    }
  };

  const handleSellService = (service: any) => {
    setSellServiceDialog({ open: true, service });
    setSaleForm({ customer: '', quantity: 1, notes: '' });
  };

  const handleCompleteSale = async () => {
    if (!sellServiceDialog.service) return;

    try {
      const customerName = saleForm.customer === 'walk_in'
        ? 'Walk-in Customer'
        : contacts.find(c => c.id === saleForm.customer)?.full_name ||
          contacts.find(c => c.id === saleForm.customer)?.company ||
          saleForm.customer || 'Customer';

      const serviceData = {
        service_id: sellServiceDialog.service.id,
        service_name: sellServiceDialog.service.name,
        customer: customerName,
        price: Number(sellServiceDialog.service.price),
        quantity: saleForm.quantity,
        notes: saleForm.notes,
      };

      const receiptData = convertServiceSaleToReceipt(serviceData);
      await addReceipt(receiptData);

      toast({
        title: 'Service sold successfully!',
        description: `Receipt generated for ${sellServiceDialog.service.name}`,
      });

      setSellServiceDialog({ open: false, service: null });
      setSaleForm({ customer: '', quantity: 1, notes: '' });
    } catch (error) {
      console.error('Error selling service:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to complete service sale',
      });
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (editingService) {
      await handleUpdateService();
    } else {
      await handleAddService();
    }
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsFormOpen(open);
    if (!open) {
      setEditingService(null);
    }
  };

  // Filtered services
  const filteredServices = useMemo(() => {
    let filtered = services;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (service.description && service.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(service => service.status === statusFilter);
    }

    // Category filter
    if (categoryFilter !== "all") {
      filtered = filtered.filter(service => service.category === categoryFilter);
    }

    // Price range filter
    if (priceRange.min || priceRange.max) {
      filtered = filtered.filter(service => {
        const price = Number(service.price);
        const min = priceRange.min ? parseFloat(priceRange.min) : 0;
        const max = priceRange.max ? parseFloat(priceRange.max) : Infinity;
        return price >= min && price <= max;
      });
    }

    return filtered;
  }, [services, searchTerm, statusFilter, categoryFilter, priceRange]);

  // Get unique categories for filter
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(services.map(service => service.category))];
    return uniqueCategories.filter(Boolean);
  }, [services]);

  const activeServices = services.filter(s => s.status === 'active').length;
  const inactiveServices = services.filter(s => s.status === 'inactive').length;
  const totalRevenue = services.reduce((sum, service) => sum + (service.total_revenue || 0), 0);
  const totalSales = services.reduce((sum, service) => sum + (service.total_sales || 0), 0);

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Services</h1>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-4 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            Services Management
          </h1>
          <p className="text-muted-foreground mt-1">Manage your service offerings and track performance</p>
        </div>
        <Dialog open={isFormOpen} onOpenChange={handleDialogOpenChange}>
          <DialogTrigger asChild>
            <Button size="lg" className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg">
              <Plus className="mr-2 h-5 w-5" /> Add Service
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>{editingService ? 'Edit Service' : 'Add New Service'}</DialogTitle>
            </DialogHeader>
            <form id="service-form" onSubmit={handleFormSubmit} className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={editingService ? editingService.name : newService.name}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="category" className="text-right">
                  Category
                </Label>
                <Input
                  id="category"
                  name="category"
                  value={editingService ? editingService.category : newService.category}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  value={editingService ? editingService.description : newService.description}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="price" className="text-right">
                  Price
                </Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  value={editingService ? editingService.price : newService.price}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right">
                  Status
                </Label>
                <Select
                  value={editingService ? editingService.status : newService.status}
                  onValueChange={handleStatusChange}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => {
                  setIsFormOpen(false);
                  setEditingService(null);
                }}>
                  Cancel
                </Button>
                <Button type="submit">
                  {editingService ? 'Update Service' : 'Add Service'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filter Controls */}
      <Card className="p-6 shadow-lg border-0 bg-gradient-to-r from-gray-50 to-white">
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search services by name, category, or description..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-10 h-12 text-lg border-2 focus:border-purple-500 transition-colors"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSearchTerm("")}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8"
              >
                ×
              </Button>
            )}
          </div>

          {/* Filter Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>

              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">View:</span>
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="text-sm text-muted-foreground">
              Showing {filteredServices.length} of {services.length} services
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-white rounded-lg border">
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Category</label>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Min Price</label>
                <Input
                  type="number"
                  placeholder="0"
                  value={priceRange.min}
                  onChange={e => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Max Price</label>
                <Input
                  type="number"
                  placeholder="No limit"
                  value={priceRange.max}
                  onChange={e => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                />
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Total Services</CardTitle>
            <div className="p-2 bg-blue-200 rounded-lg">
              <Wrench className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-700">{services.length}</div>
            <p className="text-xs text-blue-600 mt-1">All services in catalog</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Active Services</CardTitle>
            <div className="p-2 bg-green-200 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-700">{activeServices}</div>
            <p className="text-xs text-green-600 mt-1">Currently offered</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700">Total Sales</CardTitle>
            <div className="p-2 bg-purple-200 rounded-lg">
              <BarChart3 className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-700">{totalSales}</div>
            <p className="text-xs text-purple-600 mt-1">Services sold</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-yellow-700">Total Revenue</CardTitle>
            <div className="p-2 bg-yellow-200 rounded-lg">
              <DollarSign className="h-4 w-4 text-yellow-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-yellow-700">{formatCurrency(totalRevenue, currency || 'KES')}</div>
            <p className="text-xs text-yellow-600 mt-1">From services</p>
          </CardContent>
        </Card>
      </div>

      {/* Services Display */}
      <Card className="shadow-lg border-0">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-purple-600" />
            Services
            <Badge className="bg-purple-100 text-purple-700">
              {filteredServices.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredServices.length === 0 ? (
            <div className="text-center py-12">
              <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground text-lg">No services found</p>
              <p className="text-sm text-muted-foreground">Try adjusting your search or filters</p>
            </div>
          ) : viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredServices.map((service) => (
                <Card key={service.id} className="border-2 hover:border-purple-300 transition-all duration-200 hover:shadow-lg">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg leading-tight">{service.name}</CardTitle>
                        <Badge
                          variant="outline"
                          className="mt-2 text-xs"
                        >
                          {service.category}
                        </Badge>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleSellService(service)}>
                            <DollarSign className="mr-2 h-4 w-4" />
                            <span>Sell Service</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditService(service)}>
                            <Pencil className="mr-2 h-4 w-4" />
                            <span>Edit</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteService(service.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            <span>Delete</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {service.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {service.description}
                      </p>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold text-purple-600">
                        {formatCurrency(Number(service.price), currency || 'KES')}
                      </div>
                      <Badge
                        variant={service.status === 'active' ? 'default' : 'secondary'}
                        className={service.status === 'active' ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'}
                      >
                        {service.status === 'active' ? (
                          <><CheckCircle className="h-3 w-3 mr-1" />Active</>
                        ) : (
                          <><AlertTriangle className="h-3 w-3 mr-1" />Inactive</>
                        )}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                      <div className="text-center">
                        <div className="text-lg font-semibold text-blue-600">{service.total_sales || 0}</div>
                        <div className="text-xs text-muted-foreground">Sales</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">
                          {formatCurrency(service.total_revenue || 0, currency || 'KES')}
                        </div>
                        <div className="text-xs text-muted-foreground">Revenue</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredServices.map((service) => (
                <Card key={service.id} className="border-2 hover:border-purple-300 transition-all duration-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">{service.name}</h3>
                          <Badge variant="outline" className="text-xs">
                            {service.category}
                          </Badge>
                          <Badge
                            variant={service.status === 'active' ? 'default' : 'secondary'}
                            className={service.status === 'active' ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'}
                          >
                            {service.status === 'active' ? (
                              <><CheckCircle className="h-3 w-3 mr-1" />Active</>
                            ) : (
                              <><AlertTriangle className="h-3 w-3 mr-1" />Inactive</>
                            )}
                          </Badge>
                        </div>
                        {service.description && (
                          <p className="text-sm text-muted-foreground mb-2">
                            {service.description}
                          </p>
                        )}
                        <div className="flex gap-4 text-sm text-muted-foreground">
                          <span>Sales: {service.total_sales || 0}</span>
                          <span>Revenue: {formatCurrency(service.total_revenue || 0, currency || 'KES')}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="text-2xl font-bold text-purple-600">
                            {formatCurrency(Number(service.price), currency || 'KES')}
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleSellService(service)}>
                              <DollarSign className="mr-2 h-4 w-4" />
                              <span>Sell Service</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditService(service)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              <span>Edit</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDeleteService(service.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>Delete</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sell Service Dialog */}
      <Dialog open={sellServiceDialog.open} onOpenChange={(open) => setSellServiceDialog({ open, service: null })}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Sell Service: {sellServiceDialog.service?.name}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="customer" className="text-right">
                Customer
              </Label>
              <Select value={saleForm.customer} onValueChange={(value) => setSaleForm(prev => ({ ...prev, customer: value }))}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="walk_in">Walk-in Customer</SelectItem>
                  {contacts.map((contact) => (
                    <SelectItem key={contact.id} value={contact.id}>
                      {contact.full_name || contact.company}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="quantity" className="text-right">
                Quantity
              </Label>
              <Input
                id="quantity"
                type="number"
                min="1"
                value={saleForm.quantity}
                onChange={(e) => setSaleForm(prev => ({ ...prev, quantity: parseInt(e.target.value) || 1 }))}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="notes"
                value={saleForm.notes}
                onChange={(e) => setSaleForm(prev => ({ ...prev, notes: e.target.value }))}
                className="col-span-3"
                placeholder="Optional notes..."
              />
            </div>
            {sellServiceDialog.service && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Total</Label>
                <div className="col-span-3 text-lg font-semibold text-green-600">
                  {formatCurrency(Number(sellServiceDialog.service.price) * saleForm.quantity, currency || 'KES')}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setSellServiceDialog({ open: false, service: null })}>
              Cancel
            </Button>
            <Button onClick={handleCompleteSale} disabled={!saleForm.customer}>
              Complete Sale
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
