
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Search, Building, Calendar, User, MoreHorizontal } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useDeals } from "@/hooks/useDeals";

const stageColors = {
  new: { bg: "bg-blue-100", text: "text-blue-800" },
  qualified: { bg: "bg-purple-100", text: "text-purple-800" },
  proposal: { bg: "bg-yellow-100", text: "text-yellow-800" },
  negotiation: { bg: "bg-orange-100", text: "text-orange-800" },
  closed_won: { bg: "bg-green-100", text: "text-green-800" },
  closed_lost: { bg: "bg-red-100", text: "text-red-800" },
};

const getStageLabel = (stage: string) => {
  const labels: Record<string, string> = {
    new: "New",
    qualified: "Qualified",
    proposal: "Proposal",
    negotiation: "Negotiation",
    closed_won: "Won",
    closed_lost: "Lost",
  };
  return labels[stage] || stage;
};

export default function Deals() {
  const { deals, loading, addDeal, deleteDeal } = useDeals();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [newDeal, setNewDeal] = useState({
    name: '',
    amount: 0,
    stage: 'new' as const,
    probability: 0,
    owner: '',
    close_date: '',
  });

  const filteredDeals = deals.filter(deal =>
    deal.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    deal.owner?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalValue = deals.reduce((sum, deal) => sum + Number(deal.amount), 0);
  const wonValue = deals
    .filter((deal) => deal.stage === "closed_won")
    .reduce((sum, deal) => sum + Number(deal.amount), 0);
  const openValue = totalValue - wonValue;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewDeal(prevState => ({
      ...prevState,
      [name]: name === 'amount' || name === 'probability' ? parseFloat(value) || 0 : value
    }));
  };

  const handleSelectChange = (value: string) => {
    setNewDeal(prevState => ({
      ...prevState,
      stage: value as any
    }));
  };

  const handleAddDeal = async () => {
    if (!newDeal.name || !newDeal.amount || !newDeal.stage || !newDeal.owner || !newDeal.close_date) {
      return;
    }

    try {
      await addDeal(newDeal);
      setNewDeal({
        name: '',
        amount: 0,
        stage: 'new',
        probability: 0,
        owner: '',
        close_date: '',
      });
      setIsFormOpen(false);
    } catch (error) {
      // Error handled by hook
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Deals</h1>
        <div className="grid gap-4 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-2xl font-bold">Deals</h1>
        <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search deals..."
              className="pl-8 sm:w-[300px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto">
                <Plus className="mr-2 h-4 w-4" /> Add Deal
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Add New Deal</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Deal Name
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={newDeal.name}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="amount" className="text-right">
                    Amount
                  </Label>
                  <Input
                    id="amount"
                    name="amount"
                    type="number"
                    value={newDeal.amount}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="stage" className="text-right">
                    Stage
                  </Label>
                  <Select onValueChange={handleSelectChange} value={newDeal.stage}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select stage" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.keys(stageColors).map((stageKey) => (
                        <SelectItem key={stageKey} value={stageKey}>
                          {getStageLabel(stageKey)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="probability" className="text-right">
                    Probability (%)
                  </Label>
                  <Input
                    id="probability"
                    name="probability"
                    type="number"
                    min="0"
                    max="100"
                    value={newDeal.probability}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="owner" className="text-right">
                    Owner
                  </Label>
                  <Input
                    id="owner"
                    name="owner"
                    value={newDeal.owner}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="close_date" className="text-right">
                    Close Date
                  </Label>
                  <Input
                    id="close_date"
                    name="close_date"
                    type="date"
                    value={newDeal.close_date}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" onClick={handleAddDeal}>Add Deal</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Pipeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              ${openValue.toLocaleString()} open / ${wonValue.toLocaleString()} won
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Open Deals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {deals.filter((d) => !d.stage?.startsWith("closed_")).length}
            </div>
            <p className="text-xs text-muted-foreground">
              {deals.length} total deals
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Win Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {deals.length > 0 ? Math.round((deals.filter(d => d.stage === 'closed_won').length / deals.length) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Based on {deals.length} deals
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle>All Deals ({filteredDeals.length})</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          {filteredDeals.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No deals found.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Deal Name</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Stage</TableHead>
                  <TableHead>Close Date</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDeals.map((deal) => {
                  const stage = stageColors[deal.stage as keyof typeof stageColors] || 
                    { bg: "bg-gray-100", text: "text-gray-800" };
                  
                  return (
                    <TableRow key={deal.id}>
                      <TableCell className="font-medium">
                        <div className="font-medium">{deal.name}</div>
                        <div className="text-xs text-muted-foreground">
                          Created: {new Date(deal.created_at!).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          ${Number(deal.amount).toLocaleString()}
                        </div>
                        {deal.probability !== null && (
                          <div className="mt-1">
                            <Progress value={deal.probability} className="h-1.5" />
                            <div className="text-xs text-muted-foreground mt-1">
                              {deal.probability}% probability
                            </div>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge 
                          className={`${stage.bg} ${stage.text} hover:${stage.bg} capitalize`}
                        >
                          {getStageLabel(deal.stage || '')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {deal.close_date && (
                          <div className="flex items-center">
                            <Calendar className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                            <span className="text-sm">
                              {new Date(deal.close_date).toLocaleDateString()}
                            </span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {deal.owner && (
                          <div className="flex items-center">
                            <User className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                            <span className="text-sm">{deal.owner}</span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>View details</DropdownMenuItem>
                            <DropdownMenuItem>Edit</DropdownMenuItem>
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={() => deleteDeal(deal.id)}
                            >
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
