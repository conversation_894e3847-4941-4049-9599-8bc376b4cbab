
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Search, Mail, Phone, MessageSquare, PhoneCall, MessageCircle, Inbox, X, User, MessageCircleMore } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { useContacts } from "@/hooks/useContacts";

type ContactStatus = 'active' | 'inactive' | 'lead' | 'customer';

const formatPhoneNumber = (phone: string) => {
  const cleaned = ('' + phone).replace(/\D/g, '');
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
  return match ? `(${match[1]}) ${match[2]}-${match[3]}` : phone;
};

const ContactActions = ({ phone, email }: { phone: string; email: string }) => {
  const handleCall = () => {
    window.location.href = `tel:${phone}`;
  };

  const handleMessage = () => {
    window.location.href = `sms:${phone}`;
  };

  const handleWhatsApp = () => {
    let phoneNumber = phone.replace(/\D/g, '');
    if (phoneNumber.startsWith('0')) {
      phoneNumber = '1' + phoneNumber.substring(1);
    }
    window.open(`https://wa.me/${phoneNumber}`, '_blank', 'noopener,noreferrer');
  };

  const handleEmail = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!email) return;
    window.location.href = `mailto:${email}`;
  };

  return (
    <div className="flex justify-between pt-2">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleCall}>
            <PhoneCall className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Call</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleMessage}>
            <MessageSquare className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Message</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-8 w-8 text-green-600 hover:text-green-700 hover:bg-green-50" 
            onClick={handleWhatsApp}
          >
            <MessageCircle className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>WhatsApp</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50" 
            onClick={handleEmail}
          >
            <Mail className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Email</p>
        </TooltipContent>
      </Tooltip>
    </div>
  );
};

const AddContactDialog = ({ onAddContact }: { onAddContact: (contact: any) => void }) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone: '',
    company: '',
    status: 'active' as ContactStatus,
    notes: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onAddContact(formData);
    setOpen(false);
    setFormData({
      full_name: '',
      email: '',
      phone: '',
      company: '',
      status: 'active',
      notes: ''
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" /> Add Contact
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] max-h-[90vh] flex flex-col">
        <DialogHeader className="shrink-0">
          <DialogTitle>Add New Contact</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="flex-1 flex flex-col space-y-4 py-4 overflow-y-auto">
          <div className="space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              value={formData.full_name}
              onChange={(e) => setFormData({...formData, full_name: e.target.value})}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({...formData, phone: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="company">Company</Label>
            <Input
              id="company"
              value={formData.company}
              onChange={(e) => setFormData({...formData, company: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select 
              value={formData.status} 
              onValueChange={(value: ContactStatus) => 
                setFormData({...formData, status: value})
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="lead">Lead</SelectItem>
                <SelectItem value="customer">Customer</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2 flex-1 flex flex-col">
            <Label htmlFor="description">Notes</Label>
            <div className="flex-1 min-h-[100px]">
              <Textarea
                id="description"
                value={formData.notes}
                onChange={(e) => setFormData({...formData, notes: e.target.value})}
                className="h-full min-h-[100px]"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2 pt-4 mt-auto">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit">Add Contact</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default function Contacts() {
  const [searchQuery, setSearchQuery] = useState('');
  const { contacts, loading, addContact } = useContacts();

  const filteredContacts = contacts.filter(contact => 
    contact.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (contact.email && contact.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (contact.company && contact.company.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (contact.phone && contact.phone.includes(searchQuery))
  );

  const handleAddContact = async (newContact: any) => {
    try {
      await addContact(newContact);
    } catch (error) {
      console.error('Error adding contact:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-800">Contacts</h1>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-in fade-in-50 duration-500">
      {/* Header Section */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            Contacts
          </h1>
          <p className="text-base text-muted-foreground">
            Manage your business contacts and communication
          </p>
        </div>
        
        {/* Search and Add Button */}
        <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3 lg:w-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search contacts..."
              className="pl-10 w-full sm:w-[300px] md:w-[400px] bg-background/60 backdrop-blur-sm border-border/60 focus:border-primary/60 transition-all"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2"
                onClick={() => setSearchQuery('')}
              >
                <X className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
          <AddContactDialog onAddContact={handleAddContact} />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Contacts</p>
                <p className="text-2xl font-bold text-blue-900">{contacts.length}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-blue-200 flex items-center justify-center">
                <User className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Active</p>
                <p className="text-2xl font-bold text-green-900">
                  {contacts.filter(c => c.status === 'active').length}
                </p>
              </div>
              <div className="h-8 w-8 rounded-full bg-green-200 flex items-center justify-center">
                <Phone className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Leads</p>
                <p className="text-2xl font-bold text-yellow-900">
                  {contacts.filter(c => c.status === 'lead').length}
                </p>
              </div>
              <div className="h-8 w-8 rounded-full bg-yellow-200 flex items-center justify-center">
                <Plus className="h-4 w-4 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Inactive</p>
                <p className="text-2xl font-bold text-gray-900">
                  {contacts.filter(c => c.status === 'inactive').length}
                </p>
              </div>
              <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                <Inbox className="h-4 w-4 text-gray-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Contacts Grid */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {filteredContacts.map((contact) => (
          <Card key={contact.id} className="group hover:shadow-lg transition-all duration-200 hover:scale-[1.02] bg-background/80 backdrop-blur-sm border-border/60">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12 ring-2 ring-background shadow-md">
                    <AvatarFallback className="bg-gradient-to-br from-blue-100 to-purple-100 text-blue-700 font-semibold">
                      {contact.full_name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold leading-none text-gray-900">{contact.full_name}</h3>
                    {contact.company && (
                      <p className="text-sm text-muted-foreground mt-1">{contact.company}</p>
                    )}
                  </div>
                </div>
                <Badge
                  variant={
                    contact.status === "active"
                      ? "default"
                      : contact.status === "lead"
                      ? "outline"
                      : "secondary"
                  }
                  className={cn(
                    "text-xs font-medium",
                    contact.status === "active" && "bg-green-100 text-green-800 hover:bg-green-100",
                    contact.status === "lead" && "bg-blue-50 text-blue-700 hover:bg-blue-50",
                    contact.status === "inactive" && "bg-gray-100 text-gray-600 hover:bg-gray-100"
                  )}
                >
                  {contact.status.charAt(0).toUpperCase() + contact.status.slice(1)}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3 pt-0">
              <div className="space-y-2">
                {contact.phone && (
                  <div className="flex items-center text-sm group-hover:text-primary transition-colors">
                    <Phone className="h-3.5 w-3.5 mr-2 text-muted-foreground flex-shrink-0" />
                    <a 
                      href={`tel:${contact.phone}`}
                      className="text-foreground hover:underline hover:text-primary transition-colors"
                    >
                      {formatPhoneNumber(contact.phone)}
                    </a>
                  </div>
                )}
                
                {contact.email && (
                  <div className="flex items-center text-sm group-hover:text-primary transition-colors">
                    <Mail className="h-3.5 w-3.5 mr-2 text-muted-foreground flex-shrink-0" />
                    <a 
                      href={`mailto:${contact.email}`} 
                      className="text-foreground hover:underline hover:text-primary truncate transition-colors"
                      title={contact.email}
                    >
                      {contact.email}
                    </a>
                  </div>
                )}
              </div>
              
              {contact.notes && (
                <div className="pt-1">
                  <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                    {contact.notes}
                  </p>
                </div>
              )}
              
              <Separator className="my-3" />
              
              <div className="flex justify-between items-center text-xs text-muted-foreground">
                <span>Added {new Date(contact.created_at!).toLocaleDateString()}</span>
              </div>
              
              {(contact.phone || contact.email) && (
                <TooltipProvider>
                  <ContactActions phone={contact.phone || ''} email={contact.email || ''} />
                </TooltipProvider>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Empty State */}
      {filteredContacts.length === 0 && (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <div className="h-24 w-24 rounded-full bg-gray-100 flex items-center justify-center mb-6">
            <Inbox className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No contacts found</h3>
          <p className="text-base text-gray-500 mb-6 max-w-md">
            {searchQuery ? 'Try adjusting your search terms' : 'Get started by adding your first contact'}
          </p>
          {!searchQuery && (
            <AddContactDialog onAddContact={handleAddContact} />
          )}
        </div>
      )}
    </div>
  );
}
