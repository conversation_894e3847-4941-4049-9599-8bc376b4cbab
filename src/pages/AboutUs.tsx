import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, Target, BarChart3, Shield } from "lucide-react";
import { Link } from "react-router-dom";
import { PublicHeader } from "@/components/PublicHeader";
import { PublicFooter } from "@/components/PublicFooter";

export default function AboutUs() {
  const stats = [
    { value: "1000+", label: "Happy Customers" },
    { value: "24/7", label: "Support" },
    { value: "98%", label: "Satisfaction Rate" },
    { value: "5+", label: "Years Experience" }
  ];

  const features = [
    {
      icon: <Target className="w-6 h-6 text-blue-600" />,
      title: "Our Mission",
      description: "To empower businesses with innovative solutions that drive growth and efficiency."
    },
    {
      icon: <Users className="w-6 h-6 text-blue-600" />,
      title: "Our Team",
      description: "A dedicated group of experts passionate about delivering exceptional service."
    },
    {
      icon: <BarChart3 className="w-6 h-6 text-blue-600" />,
      title: "Our Approach",
      description: "Data-driven strategies tailored to your unique business needs."
    },
    {
      icon: <Shield className="w-6 h-6 text-blue-600" />,
      title: "Our Values",
      description: "Integrity, innovation, and customer satisfaction at our core."
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <PublicHeader />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-purple-50">
          <div className="max-w-7xl mx-auto text-center">
            <Badge className="mb-4 bg-blue-100 text-blue-600 border-blue-200">
              About Us
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Empowering Businesses Through Innovation
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              At vertiQ, we're committed to providing cutting-edge solutions that help businesses 
              thrive in today's competitive landscape.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button asChild>
                <Link to="/contact" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  Contact Us
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/pricing" className="text-blue-600 border-blue-600 hover:bg-blue-50">
                  View Pricing
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              {stats.map((stat, index) => (
                <div key={index} className="p-6 bg-gray-50 rounded-lg">
                  <div className="text-3xl font-bold text-blue-600 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose vertiQ?</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                We're dedicated to providing the best solutions for your business needs.
              </p>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-blue-50 flex items-center justify-center">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 md:py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 md:mb-6">Ready to Transform Your Business?</h2>
            <p className="text-base sm:text-lg md:text-xl text-blue-100 max-w-3xl mx-auto mb-6 md:mb-8">
              Join thousands of businesses that trust vertiQ to manage their operations efficiently.
            </p>
            <div className="flex justify-center">
              <Button 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-gray-100 px-8"
                asChild
              >
                <Link to="/register">Get Started</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      <PublicFooter />
    </div>
  );
}
