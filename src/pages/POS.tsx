
import { useState, use<PERSON>em<PERSON>, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useProducts } from "@/hooks/useProducts";
import { useServices } from "@/hooks/useServices";
import { useContacts } from "@/hooks/useContacts";
import { useTransactions } from "@/hooks/useTransactions";
import { useToast } from "@/components/ui/use-toast";
import { Plus, Trash2, CheckCircle2, ShoppingCart, Search, Filter, Package, Zap, AlertTriangle, CheckCircle } from "lucide-react";
import {
  Sheet,
  She<PERSON><PERSON><PERSON>ger,
  She<PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,

} from "@/components/ui/sheet";
import { useCurrency } from "@/contexts/CurrencyContext";
import { formatCurrency } from "@/utils/currency";
import { useSubscriptionContext } from "@/components/SubscriptionProvider";
import { useDataRefresh } from "@/contexts/DataRefreshContext";
import { useReceipts } from "@/hooks/useReceipts";
import { convertPOSSaleToReceipt } from "@/utils/salesToReceipt";

interface CartItem {
  id: string;
  name: string;
  type: 'product' | 'service';
  price: number;
  quantity: number;
  stock_quantity?: number;
}

export default function POS() {
  const { user } = useSubscriptionContext();
  const { products, loading: loadingProducts, updateProduct } = useProducts(user);
  const { services, loading: loadingServices } = useServices();
  const { contacts } = useContacts();
  const { addTransaction } = useTransactions(user);
  const { addReceipt } = useReceipts();
  const { toast } = useToast();
  const { currency, loading: currencyLoading } = useCurrency();
  const { refreshAll } = useDataRefresh();

  // Cart, customer, payment
  const [cart, setCart] = useState<CartItem[]>([]);
  const [customerId, setCustomerId] = useState<string>("walk_in");
  const [paymentMethod, setPaymentMethod] = useState("cash");
  const [search, setSearch] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);
  const [cartOpen, setCartOpen] = useState(false);
  const [isProcessingSale, setIsProcessingSale] = useState(false);

  // Filter states
  const [activeTab, setActiveTab] = useState("all");
  const [stockFilter, setStockFilter] = useState("all");
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [showFilters, setShowFilters] = useState(false);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[placeholder*="Search"]') as HTMLInputElement;
        searchInput?.focus();
      }

      // Ctrl/Cmd + Enter to open cart
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        setCartOpen(true);
      }

      // Escape to close cart
      if (e.key === 'Escape' && cartOpen) {
        setCartOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [cartOpen]);

  // Combine products and services
  const items = useMemo(() => {
    const productItems = products.map((p) => ({
      id: p.id,
      name: p.name,
      type: "product" as const,
      price: Number(p.price),
      stock_quantity: p.stock,
    }));

    const serviceItems = services.map((s) => ({
      id: s.id,
      name: s.name,
      type: "service" as const,
      price: Number(s.price),
      stock_quantity: undefined as number | undefined,
    }));

    return [...productItems, ...serviceItems];
  }, [products, services]);

  const filteredItems = useMemo(() => {
    let filtered = items;

    // Search filter
    if (search) {
      filtered = filtered.filter((i) =>
        i.name.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Type filter (tab-based)
    if (activeTab !== "all") {
      filtered = filtered.filter((i) => i.type === activeTab);
    }

    // Stock filter (only applies to products)
    if (stockFilter !== "all") {
      filtered = filtered.filter((i) => {
        // Services don't have stock, so include them in all filters except out_of_stock
        if (i.type === "service") {
          return stockFilter !== "out_of_stock";
        }

        if (stockFilter === "in_stock") {
          return typeof i.stock_quantity === "number" && i.stock_quantity > 0;
        }
        if (stockFilter === "low_stock") {
          return typeof i.stock_quantity === "number" && i.stock_quantity > 0 && i.stock_quantity <= 5;
        }
        if (stockFilter === "out_of_stock") {
          return typeof i.stock_quantity === "number" && i.stock_quantity === 0;
        }
        return true;
      });
    }

    // Price range filter
    if (priceRange.min || priceRange.max) {
      filtered = filtered.filter((i) => {
        const price = i.price;
        const min = priceRange.min ? parseFloat(priceRange.min) : 0;
        const max = priceRange.max ? parseFloat(priceRange.max) : Infinity;
        return price >= min && price <= max;
      });
    }

    return filtered;
  }, [items, search, activeTab, stockFilter, priceRange]);

  // Cart management
  function addToCart(item: CartItem) {
    setCart(prev => {
      const exists = prev.find(i => i.id === item.id && i.type === item.type);
      if (exists) {
        return prev.map(i =>
          i.id === item.id && i.type === item.type
            ? { ...i, quantity: i.quantity + 1 }
            : i
        );
      }
      return [...prev, { ...item, quantity: 1 }];
    });
  }

  function removeFromCart(id: string) {
    setCart(prev => prev.filter(i => i.id !== id));
  }

  function updateQuantity(id: string, delta: number) {
    setCart(prev =>
      prev.map(i =>
        i.id === id
          ? { ...i, quantity: Math.max(1, i.quantity + delta) }
          : i
      )
    );
  }

  const total = cart.reduce(
    (sum, i) => sum + i.price * i.quantity,
    0
  );

  // Complete Sale
  async function completeSale() {
    if (cart.length === 0) {
      return toast({
        title: "Cart is empty",
        description: "Add items to cart before completing sale",
        variant: "destructive"
      });
    }

    try {
      setIsProcessingSale(true);
      console.log('Starting sale completion...', { cart, user: user?.id });

      // Check stock availability first
      for (const item of cart) {
        if (item.type === "product") {
          const product = products.find(p => p.id === item.id);
          if (!product) {
            throw new Error(`Product ${item.name} not found`);
          }
          if (product.stock < item.quantity) {
            throw new Error(`Insufficient stock for ${item.name}. Available: ${product.stock}, Requested: ${item.quantity}`);
          }
        }
      }

      // Get customer name
      const customerName = customerId === "walk_in"
        ? "Walk-in Customer"
        : contacts.find(c => c.id === customerId)?.full_name || contacts.find(c => c.id === customerId)?.company || "Walk-in Customer";

      // Generate receipt using our receipt system
      const saleData = {
        customer: customerName,
        items: cart.map(item => ({
          id: item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          type: item.type,
          stock: item.stock_quantity,
        })),
        total: total,
        paymentMethod: paymentMethod,
        notes: `POS Sale - Payment: ${paymentMethod}`,
      };

      const receiptData = convertPOSSaleToReceipt(saleData);

      // Create the receipt (this will automatically handle stock updates and revenue tracking)
      console.log('Creating receipt for POS sale:', receiptData);
      await addReceipt(receiptData);

      // Process each item in the cart (for legacy transaction tracking)
      for (const item of cart) {
        let customerName: string;
        if (customerId === "walk_in") {
          customerName = "Walk-in Customer";
        } else {
          const contact = contacts.find(c => c.id === customerId);
          customerName = contact ? (contact.full_name || contact.company) : "Walk-in Customer";
        }

        console.log('Adding transaction for item:', item);

        // Add transaction record
        await addTransaction({
          type: "sale",
          description: `Sale: ${item.name} (Qty: ${item.quantity})`,
          amount: item.price * item.quantity,
          category: item.type,
          customer: customerName,
          product: item.type === "product" ? item.name : null,
          payment_method: paymentMethod,
          date: new Date().toISOString().split("T")[0],
        });

        // Stock updates are now handled by the receipt system automatically
      }

      // Clear cart and show success
      setShowSuccess(true);
      setCart([]);
      setCustomerId("walk_in");
      setPaymentMethod("cash");
      setCartOpen(false);

      setTimeout(() => setShowSuccess(false), 3000);

      toast({
        title: "Sale completed successfully!",
        description: `${cart.length} item(s) sold for ${formatCurrency(total, currency || 'KES')}`,
      });

      // Trigger data refresh across all pages
      refreshAll();

      console.log('Sale completed successfully');
    } catch (error: any) {
      console.error('Error completing sale:', error);
      toast({
        title: "Sale failed",
        description: error.message || "Something went wrong while processing the sale",
        variant: "destructive"
      });
    } finally {
      setIsProcessingSale(false);
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-4 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Point of Sale
          </h1>
          <p className="text-muted-foreground mt-1">Manage your sales and inventory</p>
        </div>

        {/* Cart Icon with badge */}
        <Sheet open={cartOpen} onOpenChange={setCartOpen}>
          <SheetTrigger asChild>
            <Button
              size="lg"
              className="relative bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg"
              onClick={() => setCartOpen(true)}
            >
              <ShoppingCart className="w-5 h-5 mr-2" />
              Cart
              {cart.length > 0 && (
                <Badge className="absolute -top-2 -right-2 bg-red-500 text-white border-0 px-2 py-1 text-xs">
                  {cart.reduce((s, i) => s + i.quantity, 0)}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="max-w-lg w-full flex flex-col">
            <SheetHeader className="border-b pb-4">
              <SheetTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <ShoppingCart className="w-6 h-6 text-blue-600" />
                </div>
                Shopping Cart
                {cart.length > 0 && (
                  <Badge className="bg-blue-600 text-white">
                    {cart.reduce((s, i) => s + i.quantity, 0)} items
                  </Badge>
                )}
              </SheetTitle>
            </SheetHeader>

            <div className="flex-1 overflow-y-auto">
              {cart.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center py-12">
                  <div className="p-4 bg-gray-100 rounded-full mb-4">
                    <ShoppingCart className="w-12 h-12 text-gray-400" />
                  </div>
                  <p className="text-lg font-medium text-muted-foreground">Your cart is empty</p>
                  <p className="text-sm text-muted-foreground">Add some products to get started</p>
                </div>
              ) : (
                <div className="space-y-4 p-4">
                  {cart.map(item => (
                    <Card
                      key={item.type + "-" + item.id}
                      className="p-4 border-2 hover:border-blue-200 transition-colors"
                    >
                      <div className="space-y-3">
                        {/* Item header */}
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-semibold text-base">{item.name}</h4>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge
                                variant={item.type === "product" ? "default" : "secondary"}
                                className="text-xs"
                              >
                                {item.type === "product" ? (
                                  <><Package className="h-3 w-3 mr-1" />Product</>
                                ) : (
                                  <><Zap className="h-3 w-3 mr-1" />Service</>
                                )}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {currencyLoading ? '-' : formatCurrency(item.price, currency || "KES")} each
                              </span>
                            </div>
                          </div>
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => removeFromCart(item.id)}
                            className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Quantity controls and total */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Button
                              size="icon"
                              variant="outline"
                              onClick={() => updateQuantity(item.id, -1)}
                              disabled={item.quantity <= 1}
                              className="h-8 w-8"
                            >
                              -
                            </Button>
                            <span className="w-12 text-center font-medium bg-gray-50 py-1 px-2 rounded">
                              {item.quantity}
                            </span>
                            <Button
                              size="icon"
                              variant="outline"
                              onClick={() => updateQuantity(item.id, 1)}
                              className="h-8 w-8"
                              disabled={
                                item.type === "product" &&
                                typeof item.stock_quantity === "number" &&
                                item.quantity >= item.stock_quantity
                              }
                            >
                              +
                            </Button>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-blue-600">
                              {currencyLoading ? '-' : formatCurrency(item.price * item.quantity, currency || "KES")}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Cart Footer */}
            {cart.length > 0 && (
              <div className="border-t bg-white p-4 space-y-4">
                {/* Customer and Payment Selection */}
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Customer</label>
                    <Select value={customerId} onValueChange={setCustomerId}>
                      <SelectTrigger className="h-9">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="walk_in">Walk-in Customer</SelectItem>
                        {contacts.map(contact => (
                          <SelectItem key={contact.id} value={contact.id}>
                            {contact.full_name || contact.company}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-1 block">Payment</label>
                    <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                      <SelectTrigger className="h-9">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="card">Card</SelectItem>
                        <SelectItem value="mobile">Mobile Money</SelectItem>
                        <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Total */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold">Total Amount</span>
                    <span className="text-2xl font-bold text-blue-600">
                      {currencyLoading ? '-' : formatCurrency(total, currency || "KES")}
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {cart.reduce((s, i) => s + i.quantity, 0)} items
                  </div>
                </div>

                {/* Complete Sale Button */}
                <Button
                  onClick={completeSale}
                  disabled={cart.length === 0 || isProcessingSale}
                  size="lg"
                  className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-3"
                >
                  {isProcessingSale ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing Sale...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="w-5 h-5 mr-2" />
                      Complete Sale
                    </>
                  )}
                </Button>
              </div>
            )}
          </SheetContent>
        </Sheet>
      </div>

      {showSuccess && (
        <div className="flex items-center gap-2 bg-green-50 rounded-lg p-4 text-green-700 border border-green-200 shadow-sm">
          <CheckCircle2 className="h-5 w-5" />
          <span className="font-medium">Sale completed successfully!</span>
        </div>
      )}

      {/* Search and Filter Controls */}
      <Card className="p-6 shadow-lg border-0 bg-gradient-to-r from-gray-50 to-white">
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search products and services... (Ctrl+K)"
              value={search}
              onChange={e => setSearch(e.target.value)}
              className="pl-10 h-12 text-lg border-2 focus:border-blue-500 transition-colors"
            />
            {search && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSearch("")}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8"
              >
                ×
              </Button>
            )}
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center justify-between">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
              <TabsList className="grid w-full grid-cols-3 max-w-md">
                <TabsTrigger value="all" className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  All
                </TabsTrigger>
                <TabsTrigger value="product" className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Products
                </TabsTrigger>
                <TabsTrigger value="service" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Services
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex items-center gap-2 ml-4">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>

              <Button
                variant="outline"
                onClick={() => setCartOpen(true)}
                className="relative"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Cart (Ctrl+Enter)
                {cart.length > 0 && (
                  <Badge className="absolute -top-2 -right-2 bg-red-500 text-white border-0 px-1 py-0 text-xs">
                    {cart.reduce((s, i) => s + i.quantity, 0)}
                  </Badge>
                )}
              </Button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-white rounded-lg border">
              <div>
                <label className="text-sm font-medium mb-2 block">Stock Status</label>
                <Select value={stockFilter} onValueChange={setStockFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All stock levels" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All stock levels</SelectItem>
                    <SelectItem value="in_stock">In stock</SelectItem>
                    <SelectItem value="low_stock">Low stock (≤5)</SelectItem>
                    <SelectItem value="out_of_stock">Out of stock</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Min Price</label>
                <Input
                  type="number"
                  placeholder="0"
                  value={priceRange.min}
                  onChange={e => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Max Price</label>
                <Input
                  type="number"
                  placeholder="No limit"
                  value={priceRange.max}
                  onChange={e => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                />
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{items.length}</div>
          <div className="text-sm text-muted-foreground">Total Items</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600">
            {items.filter(i => i.type === "product" && typeof i.stock_quantity === "number" && i.stock_quantity > 0).length}
          </div>
          <div className="text-sm text-muted-foreground">In Stock</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-yellow-600">
            {items.filter(i => i.type === "product" && typeof i.stock_quantity === "number" && i.stock_quantity > 0 && i.stock_quantity <= 5).length}
          </div>
          <div className="text-sm text-muted-foreground">Low Stock</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">{cart.length}</div>
          <div className="text-sm text-muted-foreground">Cart Items</div>
        </Card>
      </div>

      {/* Products/Services Grid */}
      <Card className="shadow-lg border-0">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">
              {activeTab === "all" ? "All Items" :
               activeTab === "product" ? "Products" : "Services"}
              <span className="text-muted-foreground ml-2">({filteredItems.length})</span>
            </h2>
            {filteredItems.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Showing {filteredItems.length} of {items.length} items
              </div>
            )}
          </div>
          <div className="max-h-96 overflow-y-auto">
            {loadingProducts || loadingServices ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-muted-foreground">Loading items...</span>
              </div>
            ) : filteredItems.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground text-lg">No items found</p>
                <p className="text-sm text-muted-foreground">Try adjusting your search or filters</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredItems.map(item => {
                  const isOutOfStock = item.type === "product" && typeof item.stock_quantity === "number" && item.stock_quantity === 0;
                  const isLowStock = item.type === "product" && typeof item.stock_quantity === "number" && item.stock_quantity > 0 && item.stock_quantity <= 5;

                  return (
                    <Card
                      key={item.id}
                      className={`p-4 transition-all duration-200 hover:shadow-md border-2 ${
                        isOutOfStock ? 'border-red-200 bg-red-50/50' :
                        isLowStock ? 'border-yellow-200 bg-yellow-50/50' :
                        'border-gray-200 hover:border-blue-300'
                      }`}
                    >
                      <div className="space-y-3">
                        {/* Header with name and type */}
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg leading-tight">{item.name}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge
                                variant={item.type === "product" ? "default" : "secondary"}
                                className="text-xs"
                              >
                                {item.type === "product" ? (
                                  <><Package className="h-3 w-3 mr-1" />Product</>
                                ) : (
                                  <><Zap className="h-3 w-3 mr-1" />Service</>
                                )}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        {/* Price */}
                        <div className="text-2xl font-bold text-blue-600">
                          {currencyLoading ? '-' : formatCurrency(item.price, currency || "KES")}
                        </div>

                        {/* Stock status */}
                        {item.type === "product" && typeof item.stock_quantity === "number" && (
                          <div className="flex items-center gap-2">
                            {isOutOfStock ? (
                              <Badge variant="destructive" className="text-xs">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Out of Stock
                              </Badge>
                            ) : isLowStock ? (
                              <Badge className="bg-yellow-500 text-white text-xs">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Low Stock ({item.stock_quantity})
                              </Badge>
                            ) : (
                              <Badge className="bg-green-500 text-white text-xs">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                In Stock ({item.stock_quantity})
                              </Badge>
                            )}
                          </div>
                        )}

                        {/* Add to cart button */}
                        <Button
                          onClick={() =>
                            addToCart({
                              id: item.id,
                              name: item.name,
                              type: item.type,
                              price: item.price,
                              quantity: 1,
                              ...(item.type === "product" && typeof item.stock_quantity === "number"
                                ? { stock_quantity: item.stock_quantity }
                                : {}),
                            })
                          }
                          disabled={isOutOfStock}
                          className={`w-full ${
                            isOutOfStock
                              ? 'bg-gray-300 cursor-not-allowed'
                              : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
                          }`}
                          size="lg"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          {isOutOfStock ? 'Out of Stock' : 'Add to Cart'}
                        </Button>
                      </div>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Checkout controls */}
      <Card className="p-4 flex flex-col md:flex-row gap-4 items-center">
        {/* Checkout controls removed since earlier */}
      </Card>
    </div>
  );
}

// End of src/pages/POS.tsx
