
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Star, 
  ArrowRight, 
  CheckCircle, 
  ChevronRight,
  Check,
  Zap,
  BarChart3,
  Users,
  FileText,
  Shield,
  Clock,
  Smartphone,
  Target,
  TrendingUp,
  MessageSquare,
  Mail,
  Linkedin,
  Twitter,
  Facebook,
  PlayCircle
} from "lucide-react";
import { useNavigate, Link } from "react-router-dom";
import { Logo } from "@/components/Logo";

export default function Homepage(): JSX.Element {
  const navigate = useNavigate();

  const stats = [
    { value: "$1M+", label: "Revenue Managed" },
    { value: "5K+", label: "Businesses" },
    { value: "98%", label: "Uptime" },
    { value: "24/7", label: "Support" }
  ];

  const features = [
    {
      icon: BarChart3,
      title: "Financial Management",
      description: "Track income, expenses, and generate financial reports with ease. Get a clear view of your business finances."
    },
    {
      icon: Users,
      title: "Customer Relationship",
      description: "Manage customer interactions, track communication, and build lasting relationships with your clients."
    },
    {
      icon: FileText,
      title: "Inventory Control",
      description: "Keep track of your stock levels, set reorder points, and manage suppliers all in one place."
    },
    {
      icon: Shield,
      title: "Data Security",
      description: "Enterprise-grade security to protect your sensitive business data and ensure compliance."
    },
    {
      icon: Clock,
      title: "Time & Attendance",
      description: "Track employee hours, manage shifts, and streamline your payroll process."
    },
    {
      icon: Smartphone,
      title: "Mobile Dashboard",
      description: "Access your business data anytime, anywhere with our responsive mobile interface."
    }
  ];

  const plans = [
    {
      name: "Starter",
      price: "$29",
      period: "per month",
      description: "Perfect for small businesses getting started",
      features: [
        "Up to 5 users",
        "Basic financial reports",
        "Email support",
        "Inventory management",
        "Mobile access"
      ],
      popular: false,
      buttonText: "Get Started"
    },
    {
      name: "Professional",
      price: "$79",
      period: "per month",
      description: "For growing businesses with more complex needs",
      features: [
        "Up to 15 users",
        "Advanced analytics",
        "Priority support",
        "CRM features",
        "Custom reporting"
      ],
      popular: true,
      buttonText: "Start Free Trial"
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "tailored solution",
      description: "For large organizations with specific requirements",
      features: [
        "Unlimited users",
        "Dedicated account manager",
        "Custom integrations",
        "On-premise deployment",
        "24/7 premium support"
      ],
      popular: false,
      buttonText: "Contact Sales"
    }
  ];

  const pricingPlans = [
    {
      name: "Monthly Plan",
      price: "10",
      period: "per month",
      description: "Perfect for businesses that prefer monthly payments",
      features: [
        "Full access to all features",
        "Regular updates",
        "Email support",
        "Cloud storage included"
      ],
      popular: true,
      buttonText: "Get Started",
      link: "/demo-login"
    },
    {
      name: "Lifetime Plan",
      price: "280",
      period: "one-time payment",
      description: "One payment, lifetime access with all updates",
      features: [
        "Full access to all features",
        "Lifetime updates",
        "Priority support",
        "Cloud storage included"
      ],
      popular: false,
      buttonText: "Get Started",
      link: "/demo-login"
    },
    {
      name: "Custom Development",
      price: "Custom",
      period: "tailored solution",
      description: "Need custom features or enterprise solutions? Let's talk!",
      features: [
        "Custom development",
        "Dedicated support",
        "Custom integrations",
        "On-premise deployment",
        "Priority feature requests"
      ],
      popular: false,
      buttonText: "Contact Us",
      link: "/contact"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <img 
                src="/vertiq lg.png" 
                alt="Vertiq Logo" 
                className="h-12 w-auto" 
              />
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-600 hover:text-blue-600 transition-colors">Home</a>
              <a href="/about" className="text-gray-600 hover:text-blue-600 transition-colors">About</a>
              <a href="/pricing" className="text-gray-600 hover:text-blue-600 transition-colors">Pricing</a>
              <a href="/contact" className="text-gray-600 hover:text-blue-600 transition-colors">Contact</a>
            </nav>
            <div className="flex items-center gap-4">
              <Button onClick={() => navigate('/demo-login')} className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                Try Demo
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-28 md:pt-32 pb-16 md:pb-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="text-center md:text-left">
              <Badge className="mb-3 md:mb-4 bg-blue-100 text-blue-600 border-blue-200 text-xs md:text-sm">
                <Zap className="w-3 h-3 mr-1.5" />
                All-in-One Business Solution
              </Badge>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 md:mb-6 leading-tight">
                Streamline Your <span className="text-blue-600">Business</span> Operations
              </h1>
              <p className="text-base sm:text-lg text-gray-600 mb-6 md:mb-8 max-w-2xl mx-auto md:mx-0">
                vertiQ helps you manage your business efficiently with powerful tools for
                financials, inventory, customers, and more - all in one platform.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center md:justify-start">
                <Button
                  asChild
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-md px-6 sm:px-8"
                >
                  <Link to="/demo-login">
                    Try Demo
                  </Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-gray-300 px-6 sm:px-8">
                  <Link to="/contact">
                    <PlayCircle className="w-4 h-4 mr-2" />
                    Contact Us
                  </Link>
                </Button>
              </div>
              <div className="mt-10 md:mt-12">
                <div className="flex overflow-x-auto pb-2 -mx-2 sm:mx-0 sm:overflow-visible justify-center md:justify-start">
                  <div className="flex gap-2 sm:gap-3 md:gap-4 text-sm text-gray-600 whitespace-nowrap px-2 sm:px-0">
                    <div className="flex items-center gap-1.5 bg-gray-50 px-3 py-1.5 rounded-full flex-shrink-0">
                      <Shield className="w-3.5 h-3.5 text-green-600 flex-shrink-0" />
                      <span className="text-xs sm:text-sm">SOC 2</span>
                    </div>
                    <div className="flex items-center gap-1.5 bg-gray-50 px-3 py-1.5 rounded-full flex-shrink-0">
                      <Zap className="w-3.5 h-3.5 text-blue-600 flex-shrink-0" />
                      <span className="text-xs sm:text-sm">99.9% Uptime</span>
                    </div>
                    <div className="flex items-center gap-1.5 bg-gray-50 px-3 py-1.5 rounded-full flex-shrink-0">
                      <Star className="w-3.5 h-3.5 text-yellow-500 fill-yellow-500 flex-shrink-0" />
                      <span className="text-xs sm:text-sm">4.8/5 Rating</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative mt-8 md:mt-0">
              <div className="relative max-w-2xl mx-auto">
                <img 
                  src="/business-management-software-lg.png" 
                  alt="Business Management Software" 
                  className="w-full h-auto rounded-2xl shadow-2xl border border-gray-200"
                />
                <div className="absolute -top-3 sm:-top-4 -right-3 sm:-right-4 bg-white rounded-full p-1.5 sm:p-2 shadow-lg border border-gray-100">
                  <BarChart3 className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
                </div>
                <div className="absolute -bottom-3 sm:-bottom-4 -left-3 sm:-left-4 bg-white rounded-lg p-2 sm:p-3 shadow-lg border border-gray-100">
                  <div className="flex items-center gap-1.5">
                    <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs sm:text-sm font-medium">Live Demo</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-12 md:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10 md:mb-12">
            <Badge className="mb-3 md:mb-4 bg-purple-100 text-purple-600 border-purple-200 text-sm">
              Features
            </Badge>
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 md:mb-4">Everything You Need to Succeed</h2>
            <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto">
              Powerful features designed to help your business grow and thrive in today's competitive landscape.
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {[
              {
                icon: <BarChart3 className="w-10 h-10 text-blue-600" />,
                title: "Financial Management",
                description: "Track income, expenses, and generate detailed financial reports with our intuitive dashboard."
              },
              {
                icon: <Users className="w-10 h-10 text-blue-600" />,
                title: "Customer Relationship",
                description: "Manage customer interactions, track communication, and build lasting relationships."
              },
              {
                icon: <FileText className="w-10 h-10 text-blue-600" />,
                title: "Invoicing & Billing",
                description: "Create professional invoices, track payments, and manage billing cycles effortlessly."
              },
              {
                icon: <Shield className="w-10 h-10 text-blue-600" />,
                title: "Security First",
                description: "Enterprise-grade security to keep your data safe and compliant with industry standards."
              },
              {
                icon: <Clock className="w-10 h-10 text-blue-600" />,
                title: "Time Tracking",
                description: "Monitor project hours and employee productivity with integrated time tracking."
              },
              {
                icon: <Smartphone className="w-10 h-10 text-blue-600" />,
                title: "Mobile Access",
                description: "Manage your business on the go with our fully responsive mobile interface."
              }
            ].map((feature, index) => (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow duration-300 border border-gray-100">
                <div className="w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-purple-100 text-purple-600 border-purple-200">
              Pricing
            </Badge>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Simple, Transparent Pricing</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose the perfect plan for your business needs. No hidden fees, cancel anytime.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <Card 
                key={plan.name}
                className={`relative overflow-hidden ${plan.popular ? 'border-2 border-blue-500' : 'border-gray-200'}`}
              >
                {plan.popular && (
                  <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs font-semibold px-3 py-1 rounded-bl-lg">
                    Most Popular
                  </div>
                )}
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <CardDescription className="text-lg">
                    <span className="text-3xl font-bold text-gray-900">${plan.price}</span>
                    {plan.period !== "tailored solution" && (
                      <span className="text-gray-500">/{plan.period}</span>
                    )}
                    {plan.period === "tailored solution" && (
                      <span className="text-gray-500"> {plan.period}</span>
                    )}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-6">{plan.description}</p>
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className={`w-full ${plan.popular ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700' : ''}`}
                    size="lg"
                    asChild
                  >
                    <a href={plan.link}>
                      Get Started
                    </a>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 md:mb-6">Ready to Transform Your Business?</h2>
          <p className="text-base sm:text-lg md:text-xl text-blue-100 max-w-3xl mx-auto mb-6 md:mb-8">
            Join thousands of businesses that trust vertiQ to manage their operations efficiently.
          </p>
          <div className="flex justify-center">
            <Button
              size="lg"
              className="bg-white text-blue-600 hover:bg-gray-100 px-8"
              onClick={() => navigate('/demo-login')}
            >
              Try Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Star className="w-5 h-5 text-yellow-400 fill-current" />
              <span className="text-2xl font-bold">4.9</span>
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
            </div>
            <p className="text-gray-600">Average rating from 500+ customers</p>
          </div>
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">1000+</div>
              <div className="text-gray-600">Active Users</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">98%</div>
              <div className="text-gray-600">Customer Satisfaction</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">50%</div>
              <div className="text-gray-600">Time Savings</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
              <div className="text-gray-600">Support Available</div>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Development Services */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Badge className="mb-4 bg-blue-100 text-blue-600 border-blue-200">
              <Zap className="w-3 h-3 mr-1" />
              Our Services
            </Badge>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Custom Development Solutions</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We build custom digital solutions tailored to your business needs
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {/* Web Development Card */}
            <Card className="hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Web Development</h3>
                <p className="text-gray-600 mb-6">
                  Custom websites and web applications built with the latest technologies to help your business grow online.
                </p>
                <div className="flex flex-wrap gap-3">
                  <Button asChild className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Link to="/contact">
                      Get a Quote
                    </Link>
                  </Button>
                  <Button variant="outline" className="border-gray-300">
                    <a href="https://wa.me/254110860589" target="_blank" rel="noopener noreferrer" className="flex items-center">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      WhatsApp Us
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Mobile App Development Card */}
            <Card className="hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Mobile App Development</h3>
                <p className="text-gray-600 mb-6">
                  Beautiful and functional mobile applications for iOS and Android that engage your users and drive growth.
                </p>
                <div className="flex flex-wrap gap-3">
                  <Button asChild className="bg-purple-600 hover:bg-purple-700 text-white">
                    <Link to="/contact">
                      Get a Quote
                    </Link>
                  </Button>
                  <Button variant="outline" className="border-gray-300">
                    <a href="https://wa.me/254110860589" target="_blank" rel="noopener noreferrer" className="flex items-center">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      WhatsApp Us
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                  <span className="text-white font-bold text-sm">vQ</span>
                </div>
                <span className="font-bold text-lg">vertiQ</span>
              </div>
              <p className="text-gray-400">
                Modern business management solution designed for the digital age.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/" className="hover:text-white transition-colors">Home</a></li>
                <li><a href="/about" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="/pricing" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="/contact" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Contact Info</h4>
              <ul className="space-y-2 text-gray-400">
                <li><EMAIL></li>
                <li>+254 110860589</li>
                <li>24/7 Support Available</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Follow Us</h4>
              <div className="flex space-x-4">
                <a href="https://wa.me/254110860589" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                  WhatsApp
                </a>
                <a href="tel:+254110860589" className="text-gray-400 hover:text-white transition-colors">
                  Call Us
                </a>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 vertiQ. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
