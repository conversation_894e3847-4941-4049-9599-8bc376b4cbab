interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  type: 'product' | 'service';
  category?: string;
  description?: string;
  stock?: number;
}

interface SaleData {
  customer?: string;
  items: CartItem[];
  total: number;
  paymentMethod?: string;
  notes?: string;
}

interface ServiceSaleData {
  service_id: string;
  service_name: string;
  customer: string;
  price: number;
  quantity?: number;
  notes?: string;
}

interface TransactionSaleData {
  description: string;
  amount: number;
  customer?: string;
  category?: string;
  notes?: string;
}

interface ReceiptItem {
  id: string;
  type: 'product' | 'service' | 'manual';
  name: string;
  description?: string;
  quantity: number;
  rate: number;
  amount: number;
  product_id?: string;
  service_id?: string;
}

interface ReceiptData {
  receipt_number: string;
  customer: string;
  due_date: string;
  amount: number;
  status: 'paid' | 'pending' | 'draft';
  items: ReceiptItem[];
  notes?: string;
}

/**
 * Convert POS sale data to receipt format
 */
export const convertPOSSaleToReceipt = (saleData: SaleData): ReceiptData => {
  const receiptItems: ReceiptItem[] = saleData.items.map((item, index) => ({
    id: `pos-item-${Date.now()}-${index}`,
    type: item.type,
    name: item.name,
    description: item.category || item.description,
    quantity: item.quantity,
    rate: item.price,
    amount: item.price * item.quantity,
    product_id: item.type === 'product' ? item.id : undefined,
    service_id: item.type === 'service' ? item.id : undefined,
  }));

  return {
    receipt_number: `POS-${Date.now()}`,
    customer: saleData.customer || 'Walk-in Customer',
    due_date: new Date().toISOString().split('T')[0], // Today's date
    amount: saleData.total,
    status: 'paid', // POS sales are always paid
    items: receiptItems,
    notes: saleData.notes || `Payment Method: ${saleData.paymentMethod || 'Cash'}`,
  };
};

/**
 * Convert service sale to receipt format
 */
export const convertServiceSaleToReceipt = (serviceData: ServiceSaleData): ReceiptData => {
  const quantity = serviceData.quantity || 1;
  const amount = serviceData.price * quantity;

  const receiptItems: ReceiptItem[] = [{
    id: `service-item-${Date.now()}`,
    type: 'service',
    name: serviceData.service_name,
    description: 'Service',
    quantity: quantity,
    rate: serviceData.price,
    amount: amount,
    service_id: serviceData.service_id,
  }];

  return {
    receipt_number: `SRV-${Date.now()}`,
    customer: serviceData.customer,
    due_date: new Date().toISOString().split('T')[0],
    amount: amount,
    status: 'paid', // Service sales are typically paid
    items: receiptItems,
    notes: serviceData.notes,
  };
};

/**
 * Convert manual transaction to receipt format
 */
export const convertTransactionToReceipt = (transactionData: TransactionSaleData): ReceiptData => {
  const receiptItems: ReceiptItem[] = [{
    id: `manual-item-${Date.now()}`,
    type: 'manual',
    name: transactionData.description,
    description: transactionData.category,
    quantity: 1,
    rate: transactionData.amount,
    amount: transactionData.amount,
  }];

  return {
    receipt_number: `TXN-${Date.now()}`,
    customer: transactionData.customer || 'Customer',
    due_date: new Date().toISOString().split('T')[0],
    amount: transactionData.amount,
    status: 'paid', // Manual sales are typically paid
    items: receiptItems,
    notes: transactionData.notes,
  };
};

/**
 * Generate a unique receipt number with prefix
 */
export const generateReceiptNumber = (prefix: string = 'RCP'): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 5).toUpperCase();
  return `${prefix}-${timestamp}-${random}`;
};

/**
 * Validate receipt data before creation
 */
export const validateReceiptData = (receiptData: ReceiptData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!receiptData.customer || receiptData.customer.trim() === '') {
    errors.push('Customer name is required');
  }

  if (!receiptData.receipt_number || receiptData.receipt_number.trim() === '') {
    errors.push('Receipt number is required');
  }

  if (receiptData.amount <= 0) {
    errors.push('Receipt amount must be greater than 0');
  }

  if (!receiptData.items || receiptData.items.length === 0) {
    errors.push('Receipt must have at least one item');
  }

  if (receiptData.items) {
    receiptData.items.forEach((item, index) => {
      if (!item.name || item.name.trim() === '') {
        errors.push(`Item ${index + 1}: Name is required`);
      }
      if (item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
      }
      if (item.rate < 0) {
        errors.push(`Item ${index + 1}: Rate cannot be negative`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Calculate receipt totals and validate consistency
 */
export const calculateReceiptTotals = (items: ReceiptItem[]): { subtotal: number; total: number; isValid: boolean } => {
  const subtotal = items.reduce((sum, item) => sum + item.amount, 0);
  const calculatedTotal = items.reduce((sum, item) => sum + (item.quantity * item.rate), 0);
  
  return {
    subtotal,
    total: calculatedTotal,
    isValid: Math.abs(subtotal - calculatedTotal) < 0.01, // Allow for small floating point differences
  };
};
