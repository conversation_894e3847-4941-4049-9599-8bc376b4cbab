// Currency utility functions for formatting and handling currencies

export const CURRENCIES = [
  { value: 'KES', label: 'Kenyan Shilling (KES)', symbol: 'KSh', locale: 'en-KE' },
  { value: 'UGX', label: 'Ugandan <PERSON>lling (UGX)', symbol: 'USh', locale: 'en-UG' },
  { value: 'TZS', label: 'Tanzanian <PERSON> (TZS)', symbol: 'TSh', locale: 'en-TZ' },
  { value: 'RWF', label: 'Rwandan Franc (RWF)', symbol: 'RWF', locale: 'en-RW' },
  { value: 'NGN', label: 'Nigerian Naira (NGN)', symbol: '₦', locale: 'en-NG' },
  { value: 'GHS', label: 'Ghanaian <PERSON> (GHS)', symbol: '₵', locale: 'en-GH' },
  { value: 'ZAR', label: 'South African Rand (ZAR)', symbol: 'R', locale: 'en-ZA' },
  { value: 'EGP', label: 'Egyptian Pound (EGP)', symbol: '£E', locale: 'ar-EG' },
  { value: 'MAD', label: 'Moroccan Dirham (MAD)', symbol: 'DH', locale: 'ar-MA' },
  { value: 'ETB', label: 'Ethiopian Birr (ETB)', symbol: 'Br', locale: 'am-ET' },
  { value: 'XOF', label: 'West African CFA Franc (XOF)', symbol: 'CFA', locale: 'fr-SN' },
  { value: 'XAF', label: 'Central African CFA Franc (XAF)', symbol: 'FCFA', locale: 'fr-CM' },
  { value: 'ZMW', label: 'Zambian Kwacha (ZMW)', symbol: 'ZK', locale: 'en-ZM' },
  { value: 'USD', label: 'US Dollar (USD)', symbol: '$', locale: 'en-US' },
];

export const DEFAULT_CURRENCY = 'KES';

/**
 * Get currency information by currency code
 */
export function getCurrencyInfo(currencyCode: string) {
  return CURRENCIES.find(currency => currency.value === currencyCode) || CURRENCIES[0];
}

/**
 * Format amount with currency symbol
 */
export function formatCurrency(amount: number, currencyCode: string = DEFAULT_CURRENCY): string {
  const currencyInfo = getCurrencyInfo(currencyCode);
  
  try {
    // Use Intl.NumberFormat for proper currency formatting
    const formatter = new Intl.NumberFormat(currencyInfo.locale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
    
    return formatter.format(amount);
  } catch (error) {
    // Fallback to manual formatting if Intl.NumberFormat fails
    const formattedAmount = amount.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
    return `${currencyInfo.symbol} ${formattedAmount}`;
  }
}

/**
 * Format amount with currency symbol (short version)
 */
export function formatCurrencyShort(amount: number, currencyCode: string = DEFAULT_CURRENCY): string {
  const currencyInfo = getCurrencyInfo(currencyCode);
  
  if (amount >= 1000000) {
    return `${currencyInfo.symbol} ${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `${currencyInfo.symbol} ${(amount / 1000).toFixed(1)}K`;
  }
  
  return formatCurrency(amount, currencyCode);
}

/**
 * Get currency symbol only
 */
export function getCurrencySymbol(currencyCode: string = DEFAULT_CURRENCY): string {
  const currencyInfo = getCurrencyInfo(currencyCode);
  return currencyInfo.symbol;
}

/**
 * Parse currency string to number
 */
export function parseCurrency(currencyString: string): number {
  // Remove all non-numeric characters except decimal point and minus sign
  const numericString = currencyString.replace(/[^\d.-]/g, '');
  return parseFloat(numericString) || 0;
}

/**
 * Validate currency code
 */
export function isValidCurrency(currencyCode: string): boolean {
  return CURRENCIES.some(currency => currency.value === currencyCode);
}

/**
 * Get all available currencies
 */
export function getAllCurrencies() {
  return CURRENCIES;
}

/**
 * Convert currency display for different contexts
 */
export function formatCurrencyForInput(amount: number, currencyCode: string = DEFAULT_CURRENCY): string {
  return amount.toFixed(2);
}

/**
 * Format currency for display in tables/lists
 */
export function formatCurrencyCompact(amount: number, currencyCode: string = DEFAULT_CURRENCY): string {
  const currencyInfo = getCurrencyInfo(currencyCode);
  
  if (amount === 0) return `${currencyInfo.symbol} 0`;
  
  if (Math.abs(amount) >= 1000000) {
    return `${currencyInfo.symbol} ${(amount / 1000000).toFixed(1)}M`;
  } else if (Math.abs(amount) >= 1000) {
    return `${currencyInfo.symbol} ${(amount / 1000).toFixed(0)}K`;
  }
  
  return `${currencyInfo.symbol} ${amount.toFixed(0)}`;
}
