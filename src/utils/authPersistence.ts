// Authentication persistence utilities
import { supabase } from '@/integrations/supabase/client';

const AUTH_STATE_KEY = 'vbms_auth_state';

export interface AuthState {
  isAuthenticated: boolean;
  userEmail: string | null;
  lastCheck: number;
}

// LocalStorage helper for secondary tab sync only
export const saveAuthState = (isAuthenticated: boolean, userEmail: string | null) => {
  const authState: AuthState = {
    isAuthenticated,
    userEmail,
    lastCheck: Date.now(),
  };
  try {
    localStorage.setItem(AUTH_STATE_KEY, JSON.stringify(authState));
  } catch (error) {
    console.error('Failed to save auth state:', error);
  }
};

export const getAuthState = (): AuthState | null => {
  try {
    const stored = localStorage.getItem(AUTH_STATE_KEY);
    if (!stored) return null;

    const authState: AuthState = JSON.parse(stored);
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    if (now - authState.lastCheck > maxAge) {
      clearAuthState();
      return null;
    }
    return authState;
  } catch (error) {
    console.error('Failed to get auth state:', error);
    return null;
  }
};

export const clearAuthState = () => {
  try {
    localStorage.removeItem(AUTH_STATE_KEY);
  } catch (error) {
    console.error('Failed to clear auth state:', error);
  }
};

// For cross-tab auth sync: listen for other tabs' logout/login events ONLY
export const initAuthPersistence = () => {
  window.addEventListener('storage', (event) => {
    if (event.key === AUTH_STATE_KEY) {
      // Auth state changed in another tab; reload or re-pull session if desired
      // The main session logic is in useAuthSession and Supabase's built-in listeners
    }
  });
};

export const shouldPreserveSession = (): boolean => {
  // You can update this logic to be more specific if required,
  // for now just always return true for session preservation.
  return true;
};
