import { supabase } from '@/integrations/supabase/client';
import { NavigateFunction } from 'react-router-dom';

export interface LogoutOptions {
  navigate: NavigateFunction;
  onSuccess?: () => void;
  onError?: (error: any) => void;
  showToast?: (toast: any) => void;
  source?: string; // For debugging which component initiated logout
}

/**
 * Centralized logout function to ensure consistent logout behavior
 * across all components
 */
export const performLogout = async ({
  navigate,
  onSuccess,
  onError,
  showToast,
  source = 'unknown'
}: LogoutOptions) => {
  try {
    console.log(`=== LOGOUT STARTED FROM ${source.toUpperCase()} ===`);
    
    // Step 1: Clear local storage first
    console.log('Clearing local storage...');
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('vbms_auth_state');
    localStorage.removeItem('sb-oijoqbyfwbyochcxdutg-auth-token'); // Supabase default key
    sessionStorage.clear();
    
    // Step 2: Sign out from Supabase
    console.log('Signing out from Supabase...');
    const { error } = await supabase.auth.signOut({
      scope: 'global' // Sign out from all sessions
    });
    
    if (error) {
      console.error('Supabase signOut error:', error);
      throw error;
    }
    
    console.log('Supabase signOut successful');
    
    // Step 3: Show success message
    if (showToast) {
      showToast({
        title: "Logged out successfully",
        description: "You have been logged out of your account.",
      });
    }
    
    // Step 4: Call success callback
    if (onSuccess) {
      onSuccess();
    }
    
    // Step 5: Navigate to login with a small delay
    setTimeout(() => {
      console.log('Navigating to login page');
      navigate('/login', { replace: true });
    }, 100);
    
    console.log('=== LOGOUT COMPLETED SUCCESSFULLY ===');
    
  } catch (error: any) {
    console.error(`Error during logout from ${source}:`, error);
    
    // Even if Supabase logout fails, clear local data and redirect
    console.log('Forcing logout cleanup...');
    localStorage.clear();
    sessionStorage.clear();
    
    if (showToast) {
      showToast({
        variant: "destructive",
        title: "Logout Error",
        description: error.message || "Failed to log out properly. Redirecting to login.",
      });
    }
    
    // Call error callback
    if (onError) {
      onError(error);
    }
    
    // Force redirect even on error
    setTimeout(() => {
      navigate('/login', { replace: true });
      // Force page reload to clear any cached state
      window.location.reload();
    }, 1000);
  }
};

/**
 * Emergency logout function for cases where normal logout fails
 * This clears everything and forces a page reload
 */
export const forceLogout = (navigate: NavigateFunction) => {
  console.log('=== FORCE LOGOUT INITIATED ===');
  
  // Clear all storage
  localStorage.clear();
  sessionStorage.clear();
  
  // Clear any cookies (if any)
  document.cookie.split(";").forEach((c) => {
    const eqPos = c.indexOf("=");
    const name = eqPos > -1 ? c.substr(0, eqPos) : c;
    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
  });
  
  // Navigate and reload
  navigate('/login', { replace: true });
  setTimeout(() => {
    window.location.reload();
  }, 100);
};

/**
 * Check if user should be logged out due to session expiry
 */
export const checkSessionValidity = async (): Promise<boolean> => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Session check error:', error);
      return false;
    }
    
    if (!session) {
      console.log('No active session found');
      return false;
    }
    
    // Check if session is expired
    const now = Math.floor(Date.now() / 1000);
    if (session.expires_at && session.expires_at < now) {
      console.log('Session expired');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error checking session validity:', error);
    return false;
  }
};
