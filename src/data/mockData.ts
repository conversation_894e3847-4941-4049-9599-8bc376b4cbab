// Sample data for the VBMS application

// Categories for products and services
export const categories = [
  // Product Categories
  { 
    id: 'cat1', 
    name: 'Electronics', 
    description: 'Electronic devices and accessories', 
    type: 'product',
    parentId: null,
    isActive: true,
    imageUrl: '/categories/electronics.jpg',
    displayOrder: 1
  },
  { 
    id: 'cat2', 
    name: 'Office Supplies', 
    description: 'Office and stationery items', 
    type: 'product',
    parentId: null,
    isActive: true,
    imageUrl: '/categories/office-supplies.jpg',
    displayOrder: 2
  },
  { 
    id: 'cat5', 
    name: 'Furniture', 
    description: 'Office furniture and equipment', 
    type: 'product',
    parentId: null,
    isActive: true,
    imageUrl: '/categories/furniture.jpg',
    displayOrder: 3
  },
  { 
    id: 'cat6', 
    name: 'Networking', 
    description: 'Networking equipment and accessories', 
    type: 'product',
    parentId: 'cat1',
    isActive: true,
    imageUrl: '/categories/networking.jpg',
    displayOrder: 4
  },
  { 
    id: 'cat7', 
    name: 'Printers & Scanners', 
    description: 'Printing and scanning solutions', 
    type: 'product',
    parentId: 'cat1',
    isActive: true,
    imageUrl: '/categories/printers.jpg',
    displayOrder: 5
  },
  
  // Service Categories
  { 
    id: 'cat3', 
    name: 'IT Services', 
    description: 'IT support and maintenance services', 
    type: 'service',
    parentId: null,
    isActive: true,
    imageUrl: '/categories/it-services.jpg',
    displayOrder: 6
  },
  { 
    id: 'cat4', 
    name: 'Business Consulting', 
    description: 'Business strategy and consulting services', 
    type: 'service',
    parentId: null,
    isActive: true,
    imageUrl: '/categories/consulting.jpg',
    displayOrder: 7
  },
  { 
    id: 'cat8', 
    name: 'Cloud Services', 
    description: 'Cloud computing and storage solutions', 
    type: 'service',
    parentId: 'cat3',
    isActive: true,
    imageUrl: '/categories/cloud.jpg',
    displayOrder: 8
  },
  { 
    id: 'cat9', 
    name: 'Cybersecurity', 
    description: 'Security and compliance services', 
    type: 'service',
    parentId: 'cat3',
    isActive: true,
    imageUrl: '/categories/security.jpg',
    displayOrder: 9
  },
  { 
    id: 'cat10', 
    name: 'Digital Marketing', 
    description: 'Online marketing and advertising services', 
    type: 'service',
    parentId: 'cat4',
    isActive: true,
    imageUrl: '/categories/marketing.jpg',
    displayOrder: 10
  }
];

// Sample products
export const products = [
  {
    id: 'prod1',
    name: 'Laptop Pro X1',
    sku: 'LP-X1-001',
    categoryId: 'cat1',
    description: 'High-performance business laptop',
    price: 1299.99,
    cost: 899.99,
    taxRate: 0.2,
    active: true,
    createdAt: '2025-01-15T10:00:00Z',
  },
  {
    id: 'prod2',
    name: 'Ergonomic Keyboard',
    sku: 'KB-ERG-202',
    categoryId: 'cat1',
    description: 'Comfortable ergonomic keyboard',
    price: 129.99,
    cost: 59.99,
    taxRate: 0.2,
    active: true,
    createdAt: '2025-02-20T14:30:00Z',
  },
  {
    id: 'prod3',
    name: 'Office Chair',
    sku: 'FUR-CHR-101',
    categoryId: 'cat5',
    description: 'Ergonomic office chair with lumbar support',
    price: 299.99,
    cost: 179.99,
    taxRate: 0.2,
    active: true,
    createdAt: '2025-03-10T09:15:00Z',
  },
];

// Sample services
export const services = [
  {
    id: 'srv1',
    name: 'IT Support - Basic',
    categoryId: 'cat3',
    description: 'Basic IT support services including troubleshooting and maintenance',
    price: 99.99,
    cost: 49.99,
    duration: 60, // minutes
    active: true,
    isRecurring: true,
    recurrence: 'monthly',
  },
  {
    id: 'srv2',
    name: 'Business Strategy Session',
    categoryId: 'cat4',
    description: 'One-on-one business strategy consultation',
    price: 299.99,
    cost: 150.00,
    duration: 120, // minutes
    active: true,
    isRecurring: false,
  },
];

// Stock levels for products
export const stock = [
  { productId: 'prod1', quantity: 15, location: 'Warehouse A', lastUpdated: '2025-05-28T08:00:00Z' },
  { productId: 'prod2', quantity: 42, location: 'Warehouse B', lastUpdated: '2025-05-29T10:30:00Z' },
  { productId: 'prod3', quantity: 8, location: 'Warehouse A', lastUpdated: '2025-05-30T14:15:00Z' },
];

// Sample reports
export const reports = {
  // Sales Reports
  salesSummary: {
    id: 'sales-summary',
    title: 'Sales Summary Report',
    description: 'Comprehensive sales performance overview',
    type: 'sales',
    data: {
      period: { start: '2025-05-01', end: '2025-05-31' },
      totalSales: 125487.75,
      totalOrders: 423,
      averageOrderValue: 296.66,
      newCustomers: 87,
      repeatCustomers: 336,
      byCategory: [
        { category: 'Electronics', amount: 58975.50, percentage: 47 },
        { category: 'IT Services', amount: 32500.25, percentage: 26 },
        { category: 'Business Consulting', amount: 18750.00, percentage: 15 },
        { category: 'Furniture', amount: 9814.00, percentage: 8 },
        { category: 'Office Supplies', amount: 5448.00, percentage: 4 },
      ],
      topProducts: [
        { id: 'prod1', name: 'Laptop Pro X1', quantity: 42, revenue: 54999.16 },
        { id: 'prod4', name: 'Network Switch', quantity: 15, revenue: 8999.25 },
        { id: 'srv1', name: 'IT Support - Basic', quantity: 87, revenue: 8699.13 },
        { id: 'srv2', name: 'Business Strategy Session', quantity: 32, revenue: 9599.68 },
        { id: 'prod3', name: 'Office Chair', quantity: 18, revenue: 5399.82 },
      ],
      salesTrend: [
        { date: '2025-05-01', amount: 3875.25 },
        { date: '2025-05-08', amount: 4125.50 },
        { date: '2025-05-15', amount: 5234.75 },
        { date: '2025-05-22', amount: 4875.30 },
        { date: '2025-05-31', amount: 6125.90 },
      ]
    }
  },
  
  // Inventory Reports
  inventoryStatus: {
    id: 'inventory-status',
    title: 'Inventory Status Report',
    description: 'Current inventory levels and stock status',
    type: 'inventory',
    data: {
      asOf: '2025-06-05T14:30:00Z',
      totalProducts: 28,
      totalValue: 189745.89,
      inventoryTurnover: 4.2,
      daysOfInventory: 23,
      
      stockStatus: {
        inStock: 18,
        lowStock: 7,
        outOfStock: 3,
        onOrder: 5
      },
      
      lowStock: [
        { id: 'prod3', name: 'Office Chair', quantity: 8, reorderLevel: 10, location: 'Warehouse A' },
        { id: 'prod6', name: 'Wireless Mouse', quantity: 5, reorderLevel: 15, location: 'Warehouse B' },
        { id: 'prod12', name: 'Monitor Stand', quantity: 2, reorderLevel: 5, location: 'Warehouse A' },
      ],
      
      outOfStock: [
        { id: 'prod8', name: 'HDMI Cables', quantity: 0, reorderLevel: 20, location: 'Warehouse B' },
        { id: 'prod15', name: 'Laptop Stand', quantity: 0, reorderLevel: 8, location: 'Warehouse A' },
      ],
      
      topMoving: [
        { id: 'prod1', name: 'Laptop Pro X1', quantitySold: 42, turnoverRate: 8.4 },
        { id: 'prod4', name: 'Network Switch', quantitySold: 15, turnoverRate: 6.2 },
        { id: 'prod7', name: 'Wireless Keyboard', quantitySold: 28, turnoverRate: 5.8 },
      ]
    }
  },
  
  // Financial Reports
  financialSummary: {
    id: 'financial-summary',
    title: 'Financial Summary Report',
    description: 'Comprehensive financial performance overview',
    type: 'financial',
    data: {
      period: { start: '2025-04-01', end: '2025-06-30' },
      
      // Income Statement
      incomeStatement: {
        revenue: 387452.68,
        cogs: 214589.32,
        grossProfit: 172863.36,
        operatingExpenses: 98745.21,
        operatingIncome: 74118.15,
        otherIncome: 1250.00,
        otherExpenses: 3250.50,
        netIncome: 72117.65,
        ebitda: 85620.40,
        profitMargin: 18.6
      },
      
      // Balance Sheet
      balanceSheet: {
        assets: {
          current: {
            cash: 125487.32,
            accountsReceivable: 87542.10,
            inventory: 189745.89,
            prepaidExpenses: 12500.00,
            totalCurrent: 415275.31
          },
          fixed: {
            property: 250000.00,
            equipment: 125487.32,
            accumulatedDepreciation: -62548.66,
            totalFixed: 312938.66
          },
          totalAssets: 728213.97
        },
        liabilities: {
          current: {
            accountsPayable: 87542.10,
            shortTermDebt: 50000.00,
            accruedExpenses: 25487.32,
            totalCurrent: 163029.42
          },
          longTerm: {
            longTermDebt: 150000.00,
            deferredTax: 25487.32,
            totalLongTerm: 175487.32
          },
          totalLiabilities: 338516.74
        },
        equity: {
          commonStock: 250000.00,
          retainedEarnings: 139697.23,
          totalEquity: 389697.23
        }
      },
      
      // Cash Flow
      cashFlow: {
        operating: 125487.32,
        investing: -62548.66,
        financing: -25000.00,
        netChange: 37938.66,
        beginningCash: 87548.66,
        endingCash: 125487.32
      },
      
      // Key Metrics
      keyMetrics: {
        currentRatio: 2.55,
        quickRatio: 1.38,
        debtToEquity: 0.45,
        returnOnAssets: 0.12,
        returnOnEquity: 0.21
      }
    }
  },
  
  // Customer Reports
  customerAnalysis: {
    id: 'customer-analysis',
    title: 'Customer Analysis Report',
    description: 'Customer segmentation and behavior analysis',
    type: 'customer',
    data: {
      totalCustomers: 587,
      newCustomers: 124,
      repeatCustomers: 463,
      customerRetentionRate: 82.5,
      
      bySegment: [
        { segment: 'Small Business', count: 245, percentage: 41.7 },
        { segment: 'Enterprise', count: 158, percentage: 26.9 },
        { segment: 'Individual', count: 184, percentage: 31.4 },
      ],
      
      byLocation: [
        { location: 'North America', count: 325, percentage: 55.4 },
        { location: 'Europe', count: 158, percentage: 26.9 },
        { location: 'Asia', count: 87, percentage: 14.8 },
        { location: 'Other', count: 17, percentage: 2.9 },
      ],
      
      topCustomers: [
        { id: 'cust101', name: 'Acme Corp', revenue: 125487.32, orders: 42 },
        { id: 'cust205', name: 'Globex Inc', revenue: 98745.21, orders: 35 },
        { id: 'cust312', name: 'Initech LLC', revenue: 87542.10, orders: 28 },
        { id: 'cust478', name: 'Umbrella Corp', revenue: 75421.98, orders: 24 },
        { id: 'cust522', name: 'Stark Industries', revenue: 65487.32, orders: 19 },
      ],
      
      customerLifetimeValue: 5241.32,
      averagePurchaseValue: 587.45,
      averagePurchaseFrequency: 3.2,
      churnRate: 4.8
    }
  },
  
  // Product Performance
  productPerformance: {
    id: 'product-performance',
    title: 'Product Performance Report',
    description: 'Sales and profitability by product',
    type: 'product',
    data: {
      totalProducts: 42,
      topSelling: [
        { 
          id: 'prod1', 
          name: 'Laptop Pro X1', 
          category: 'Electronics',
          unitsSold: 125, 
          revenue: 162487.25,
          cost: 112487.25,
          profit: 50000.00,
          margin: 30.8
        },
        { 
          id: 'srv1', 
          name: 'IT Support - Basic', 
          category: 'IT Services',
          unitsSold: 245, 
          revenue: 24475.50,
          cost: 12237.75,
          profit: 12237.75,
          margin: 50.0
        },
        { 
          id: 'prod3', 
          name: 'Office Chair', 
          category: 'Furniture',
          unitsSold: 42, 
          revenue: 12599.58,
          cost: 7559.75,
          profit: 5039.83,
          margin: 40.0
        },
      ],
      
      byCategory: [
        { 
          category: 'Electronics', 
          revenue: 215487.25,
          cost: 150841.08,
          profit: 64646.17,
          margin: 30.0
        },
        { 
          category: 'IT Services', 
          revenue: 187542.10,
          cost: 93771.05,
          profit: 93771.05,
          margin: 50.0
        },
        { 
          category: 'Furniture', 
          revenue: 125487.32,
          cost: 75292.39,
          profit: 50194.93,
          margin: 40.0
        },
      ],
      
      inventoryTurnover: 4.2,
      daysInInventory: 87,
      stockoutRate: 2.8
    }
  }
};

// Helper function to get product with stock info
export const getProductsWithStock = () => {
  return products.map(product => ({
    ...product,
    stock: stock.find(s => s.productId === product.id)?.quantity || 0,
    location: stock.find(s => s.productId === product.id)?.location || 'N/A',
  }));
};

// Helper function to get services with category info
export const getServicesWithCategory = () => {
  return services.map(service => ({
    ...service,
    category: categories.find(cat => cat.id === service.categoryId)?.name || 'Uncategorized'
  }));
};
