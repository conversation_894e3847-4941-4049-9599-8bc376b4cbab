import React, { createContext, useContext, useState, useCallback } from 'react';

interface DataRefreshContextType {
  refreshTrigger: number;
  triggerRefresh: () => void;
  refreshProducts: () => void;
  refreshTransactions: () => void;
  refreshAll: () => void;
}

const DataRefreshContext = createContext<DataRefreshContextType | undefined>(undefined);

export function DataRefreshProvider({ children }: { children: React.ReactNode }) {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const triggerRefresh = useCallback(() => {
    setRefreshTrigger(prev => prev + 1);
  }, []);

  const refreshProducts = useCallback(() => {
    console.log('Triggering products refresh');
    triggerRefresh();
  }, [triggerRefresh]);

  const refreshTransactions = useCallback(() => {
    console.log('Triggering transactions refresh');
    triggerRefresh();
  }, [triggerRefresh]);

  const refreshAll = useCallback(() => {
    console.log('Triggering all data refresh');
    triggerRefresh();
  }, [triggerRefresh]);

  return (
    <DataRefreshContext.Provider value={{
      refreshTrigger,
      triggerRefresh,
      refreshProducts,
      refreshTransactions,
      refreshAll,
    }}>
      {children}
    </DataRefreshContext.Provider>
  );
}

export function useDataRefresh() {
  const context = useContext(DataRefreshContext);
  if (context === undefined) {
    // Return a no-op implementation if context is not available
    console.warn('useDataRefresh used outside of DataRefreshProvider, returning no-op implementation');
    return {
      refreshTrigger: 0,
      triggerRefresh: () => {},
      refreshProducts: () => {},
      refreshTransactions: () => {},
      refreshAll: () => {},
    };
  }
  return context;
}
