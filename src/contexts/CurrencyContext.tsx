
import React, { createContext, useContext, useMemo } from "react";
import { useAuth } from "./AuthContext";
import { useUserSettings } from "@/hooks/useUserSettings";

/**
 * Context provides user's selected currency across the entire application.
 */
type CurrencyContextType = {
  currency: string | null;
  loading: boolean;
  // Add more if convenient (e.g., updateCurrency)
};

const CurrencyContext = createContext<CurrencyContextType>({ currency: null, loading: true });

export const CurrencyProvider = ({ children }: { children: React.ReactNode }) => {
  const { user } = useAuth();
  const { settings, loading } = useUserSettings(user);

  // NOTE: Always prefer user_settings.user_currency over fallback
  const currency = useMemo(() => {
    return settings?.user_currency || settings?.currency || "KES";
  }, [settings]);

  return (
    <CurrencyContext.Provider value={{ currency, loading }}>
      {children}
    </CurrencyContext.Provider>
  );
};

export function useCurrency() {
  return useContext(CurrencyContext);
}
