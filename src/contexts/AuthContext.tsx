import React, { createContext, useContext, useState, useEffect, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import type { User, Session } from "@supabase/supabase-js";

interface AuthContextProps {
  user: User | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextProps>({
  user: null,
  session: null,
  loading: true,
  error: null,
});

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Ref to store last handled supabase event timestamp
  const lastEventTs = useRef<number>(0);
  const rateLimitMs = 5000; // Increased to 5 seconds for better stability

  useEffect(() => {
    let mounted = true;

    function debug(...args: any[]) {
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.log("[AuthContext]", ...args);
      }
    }

    // Only handle supabase events that are outside the rate limit
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      const now = Date.now();
      if (now - lastEventTs.current < rateLimitMs) {
        debug("Rate limit: skipping auth event", event, session?.user?.email);
        setError("Rate-limited by auth provider, try again in a few seconds.");
        return;
      }
      lastEventTs.current = now;

      debug("Auth event:", event, session?.user?.email);
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
      setError(null);

      if (!session || !session.user) {
        debug("User signed out or session missing");
        // Clear any remaining local storage on sign out
        if (event === "SIGNED_OUT") {
          localStorage.removeItem('supabase.auth.token');
          localStorage.removeItem('vbms_auth_state');
        }
      }
      if (event === "SIGNED_OUT") {
        debug("SIGNED_OUT - Clearing local storage");
        // Ensure clean logout state
        localStorage.removeItem('supabase.auth.token');
        localStorage.removeItem('vbms_auth_state');
        sessionStorage.clear();
      }
      if (event === "SIGNED_IN") {
        debug("SIGNED_IN", session?.user?.email);
      }
      if (event === "TOKEN_REFRESHED") {
        debug("TOKEN_REFRESHED");
      }
      if (event === "USER_UPDATED") {
        debug("USER_UPDATED", session?.user?.email);
      }
    });

    // Only check for session after listener so we do not miss any events
    supabase.auth.getSession().then(({ data }) => {
      if (!mounted) return;
      setSession(data.session ?? null);
      setUser(data.session?.user ?? null);
      setLoading(false);
      setError(null);
      if (data.session && data.session.user) {
        debug("Initial session:", data.session.user.email);
      } else {
        debug("No session at initial load");
      }
    }).catch((err) => {
      debug("Error fetching session:", err);
      setLoading(false);
      setError("Could not get session. Network or provider error?");
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  return (
    <AuthContext.Provider value={{ user, session, loading, error }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
