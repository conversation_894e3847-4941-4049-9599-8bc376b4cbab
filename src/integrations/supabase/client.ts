
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Use explicit localStorage and implicit flow, disable auto-refresh!
const SUPABASE_URL = "https://oijoqbyfwbyochcxdutg.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9pam9xYnlmd2J5b2NoY3hkdXRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyOTA0NTYsImV4cCI6MjA2NDg2NjQ1Nn0.9Awh90PuE4mPr5UBK-410FdqXTqmUMWDtqUJPQshmYk";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    autoRefreshToken: false, // <--- disables background token refreshes that cause logout
    persistSession: true,
    detectSessionInUrl: false,
    flowType: 'implicit',
    storage: window.localStorage,
    storageKey: 'supabase.auth.token'
  }
});
