-- Currency System Update Migration
-- This migration ensures all users have KES as default currency and updates the system to handle user-selected currencies

-- 1. Update all existing users to have KES as default if they don't have a currency set
UPDATE public.user_settings 
SET 
  currency = COALESCE(currency, 'KES'),
  user_currency = COALESCE(user_currency, currency, 'KES')
WHERE currency IS NULL OR user_currency IS NULL;

-- 2. Set KES as default for any remaining null values
UPDATE public.user_settings 
SET 
  currency = 'KES',
  user_currency = 'KES'
WHERE currency IS NULL OR user_currency IS NULL;

-- 3. Update the handle_new_user function to properly handle currency from registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  -- Get the user's selected currency from registration metadata
  DECLARE
    selected_currency TEXT := COALESCE(NEW.raw_user_meta_data ->> 'user_currency', NEW.raw_user_meta_data ->> 'currency', 'KES');
  BEGIN
    INSERT INTO public.user_settings (
      user_id, 
      display_name, 
      email, 
      country, 
      currency, 
      user_currency,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      COALESCE(NEW.raw_user_meta_data ->> 'full_name', ''),
      COALESCE(NEW.email, ''),
      COALESCE(NEW.raw_user_meta_data ->> 'country', 'Kenya'),
      selected_currency,
      selected_currency,
      NOW(),
      NOW()
    );
    RETURN NEW;
  END;
END;
$function$;

-- 4. Ensure the trigger exists and is properly set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 5. Add indexes for better performance on currency queries
CREATE INDEX IF NOT EXISTS idx_user_settings_currency ON public.user_settings(currency);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_currency ON public.user_settings(user_currency);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON public.user_settings(user_id);

-- 6. Add a check constraint to ensure valid currency codes (optional)
-- This ensures only valid currency codes can be stored
ALTER TABLE public.user_settings 
DROP CONSTRAINT IF EXISTS check_valid_currency;

ALTER TABLE public.user_settings 
ADD CONSTRAINT check_valid_currency 
CHECK (currency IN ('KES', 'UGX', 'TZS', 'RWF', 'NGN', 'GHS', 'ZAR', 'EGP', 'MAD', 'ETB', 'XOF', 'XAF', 'ZMW', 'USD'));

ALTER TABLE public.user_settings 
DROP CONSTRAINT IF EXISTS check_valid_user_currency;

ALTER TABLE public.user_settings 
ADD CONSTRAINT check_valid_user_currency 
CHECK (user_currency IN ('KES', 'UGX', 'TZS', 'RWF', 'NGN', 'GHS', 'ZAR', 'EGP', 'MAD', 'ETB', 'XOF', 'XAF', 'ZMW', 'USD'));

-- 7. Update any transactions or financial records to use user's preferred currency
-- (This would be expanded based on your specific transaction table structure)

-- 8. Create a function to get user's preferred currency
CREATE OR REPLACE FUNCTION public.get_user_currency(user_uuid UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  user_curr TEXT;
BEGIN
  SELECT user_currency INTO user_curr
  FROM public.user_settings
  WHERE user_id = user_uuid;
  
  RETURN COALESCE(user_curr, 'KES');
END;
$function$;

-- 9. Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.get_user_currency(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO service_role;
