
-- Create a table to track user payments
CREATE TABLE public.user_payments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  amount DECIMAL(10,2) NOT NULL DEFAULT 10.00,
  currency VARCHAR(3) NOT NULL DEFAULT 'KES',
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  pesapal_tracking_id VARCHAR(255),
  pesapal_merchant_reference VARCHAR(255),
  payment_method VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  paid_at TIMESTAMP WITH TIME ZONE
);

-- Add RLS policies for user payments
ALTER TABLE public.user_payments ENABLE ROW LEVEL SECURITY;

-- Users can view their own payments
CREATE POLICY "Users can view their own payments" 
  ON public.user_payments 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can create their own payments
CREATE POLICY "Users can create their own payments" 
  ON public.user_payments 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own payments (for webhook updates)
CREATE POLICY "Users can update their own payments" 
  ON public.user_payments 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Add updated_at trigger
CREATE TRIGGER update_user_payments_updated_at
  BEFORE UPDATE ON public.user_payments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Add a column to user_settings to track if user has paid
ALTER TABLE public.user_settings 
ADD COLUMN has_paid BOOLEAN NOT NULL DEFAULT false;
