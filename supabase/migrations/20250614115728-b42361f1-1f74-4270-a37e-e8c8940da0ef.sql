
-- 1. Update currency to 'KES' for all existing users
UPDATE public.user_settings
SET currency = 'KES', user_currency = 'KES';

-- 2. Update the handle_new_user trigger function for future users
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  INSERT INTO public.user_settings (user_id, display_name, email, country, currency, user_currency)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data ->> 'full_name',
    NEW.email,
    COALESCE(NEW.raw_user_meta_data ->> 'country', 'Kenya'),
    COALESCE(NEW.raw_user_meta_data ->> 'currency', 'KES'),
    COALESCE(NEW.raw_user_meta_data ->> 'user_currency', NEW.raw_user_meta_data ->> 'currency', 'KES')
  );
  RETURN NEW;
END;
$function$;
