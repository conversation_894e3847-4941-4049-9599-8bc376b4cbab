
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    console.log('Pesapal initiate function called')
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('No authorization header provided')
      throw new Error('No authorization header')
    }

    // Get user from token
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    )
    
    if (authError || !user) {
      console.error('Authentication error:', authError)
      throw new Error('Invalid authentication')
    }

    console.log('Authenticated user:', user.id)

    const { amount = 0.10, currency = 'USD' } = await req.json()

    // Get Pesapal credentials from environment
    let consumerKey = Deno.env.get('PESAPAL_CONSUMER_KEY')
    let consumerSecret = Deno.env.get('PESAPAL_CONSUMER_SECRET')

    console.log('Environment credentials found:', !!consumerKey, !!consumerSecret)

    // Test the credentials first
    if (consumerKey && consumerSecret) {
      console.log('Testing environment credentials...')
      const testResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Auth/RequestToken', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          consumer_key: consumerKey,
          consumer_secret: consumerSecret
        })
      })

      const testData = await testResponse.json()
      console.log('Credential test result:', testData)

      if (!testData.token) {
        console.log('Environment credentials failed, falling back to demo credentials')
        consumerKey = 'qkio1BGGYAXTu2JOfm7XSXNruoZsrqEW'
        consumerSecret = 'osGQ364R49cXKeOYSpaOnT++rHs='
      } else {
        console.log('Environment credentials working!')
      }
    } else {
      console.log('No environment credentials found, using demo credentials for testing')
      consumerKey = 'qkio1BGGYAXTu2JOfm7XSXNruoZsrqEW'
      consumerSecret = 'osGQ364R49cXKeOYSpaOnT++rHs='
    }

    console.log('Using consumer key:', consumerKey?.substring(0, 10) + '...')

    console.log('Pesapal credentials found, proceeding with payment')

    // Generate a unique merchant reference
    const merchantReference = `vbms_${user.id}_${Date.now()}`
    
    // Create payment record in database
    const { data: paymentData, error: dbError } = await supabaseClient
      .from('user_payments')
      .insert({
        user_id: user.id,
        amount: amount,
        currency: currency,
        status: 'pending',
        pesapal_merchant_reference: merchantReference
      })
      .select()
      .single()

    if (dbError) {
      console.error('Database error:', dbError)
      throw new Error('Failed to create payment record')
    }

    console.log('Payment record created:', paymentData.id)

    // Get access token from Pesapal
    const tokenResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Auth/RequestToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        consumer_key: consumerKey,
        consumer_secret: consumerSecret
      })
    })

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text()
      console.error('Pesapal token request failed:', {
        status: tokenResponse.status,
        statusText: tokenResponse.statusText,
        error: errorText
      })
      throw new Error(`Failed to get Pesapal access token: ${errorText}`)
    }

    const tokenData = await tokenResponse.json()
    console.log('Token response data:', tokenData)

    if (!tokenData.token) {
      console.error('No token in response:', tokenData)
      throw new Error('Invalid token response from Pesapal')
    }

    const accessToken = tokenData.token
    console.log('Pesapal access token obtained successfully')

    // Register IPN URL
    const ipnResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/URLSetup/RegisterIPN', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        url: `${Deno.env.get('SUPABASE_URL')}/functions/v1/pesapal-webhook`,
        ipn_notification_type: 'GET'
      })
    })

    if (!ipnResponse.ok) {
      const ipnErrorText = await ipnResponse.text()
      console.error('IPN registration failed:', {
        status: ipnResponse.status,
        error: ipnErrorText
      })
      // Continue anyway as IPN might not be required for initial testing
    }

    const ipnData = await ipnResponse.json()
    console.log('IPN Registration response:', ipnData)

    // Submit order request
    const orderData = {
      id: merchantReference,
      currency: 'KES', // Change to KES as Pesapal primarily supports East African currencies
      amount: amount, // Amount is already in KES
      description: 'vertiQ Business Management Software - One-time Payment',
      callback_url: `${Deno.env.get('SUPABASE_URL')}/functions/v1/pesapal-callback`,
      notification_id: ipnData.ipn_id || '',
      billing_address: {
        email_address: user.user_metadata?.email || user.email,
        phone_number: '+254700000000', // Default phone number
        country_code: 'KE',
        first_name: user.user_metadata?.full_name?.split(' ')[0] || 'User',
        last_name: user.user_metadata?.full_name?.split(' ')[1] || 'Name',
        line_1: 'Default Address', // Provide default address
        line_2: '',
        city: 'Nairobi',
        state: 'Nairobi',
        postal_code: '00100',
        zip_code: '00100'
      }
    }

    console.log('Submitting order to Pesapal:', orderData)

    const orderResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Transactions/SubmitOrderRequest', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify(orderData)
    })

    if (!orderResponse.ok) {
      const errorText = await orderResponse.text()
      console.error('Pesapal order submission failed:', {
        status: orderResponse.status,
        statusText: orderResponse.statusText,
        error: errorText,
        orderData: orderData
      })
      throw new Error(`Failed to submit order to Pesapal: ${errorText}`)
    }

    const orderResult = await orderResponse.json()
    console.log('Pesapal order result:', orderResult)

    if (!orderResult.redirect_url) {
      console.error('No redirect URL in order result:', orderResult)
      throw new Error('Invalid order response - no redirect URL')
    }
    
    // Update payment record with tracking ID
    await supabaseClient
      .from('user_payments')
      .update({
        pesapal_tracking_id: orderResult.tracking_id
      })
      .eq('id', paymentData.id)

    console.log('Payment initiation successful')

    return new Response(
      JSON.stringify({
        success: true,
        redirect_url: orderResult.redirect_url,
        tracking_id: orderResult.tracking_id,
        merchant_reference: merchantReference
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Pesapal initiate error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Unknown error occurred'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
