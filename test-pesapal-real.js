// Test script to debug Pesapal integration with real credentials
// Note: This is for debugging only - credentials should be kept secure

// These are the encrypted values from Supabase - we need to decode them
// For now, let's test the API structure and see what errors we get

async function testPesapalStructure() {
  try {
    console.log('Testing Pesapal API structure...');
    
    // Test with empty credentials to see the error format
    const tokenResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Auth/RequestToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        consumer_key: '',
        consumer_secret: ''
      })
    });

    console.log('Response status:', tokenResponse.status);
    console.log('Response headers:', Object.fromEntries(tokenResponse.headers.entries()));
    
    const responseText = await tokenResponse.text();
    console.log('Response body:', responseText);
    
    try {
      const responseJson = JSON.parse(responseText);
      console.log('Parsed response:', responseJson);
    } catch (e) {
      console.log('Response is not valid JSON');
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Test the API endpoint availability
async function testEndpointAvailability() {
  try {
    console.log('\nTesting endpoint availability...');
    
    const response = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Auth/RequestToken', {
      method: 'GET'
    });
    
    console.log('GET response status:', response.status);
    const text = await response.text();
    console.log('GET response:', text);
    
  } catch (error) {
    console.error('Endpoint test failed:', error);
  }
}

// Run the tests
testPesapalStructure();
testEndpointAvailability();
