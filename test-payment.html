<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pesapal Payment</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #007bff;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Pesapal Payment Integration Test</h1>
        
        <div>
            <h3>Test Steps:</h3>
            <ol>
                <li>Sign in with test credentials</li>
                <li>Test payment initiation</li>
                <li>Check payment status</li>
            </ol>
        </div>

        <div>
            <button class="button" onclick="signIn()">1. Sign In</button>
            <button class="button" onclick="testPayment()" id="paymentBtn" disabled>2. Test Payment</button>
            <button class="button" onclick="checkStatus()" id="statusBtn" disabled>3. Check Status</button>
            <button class="button" onclick="clearLog()">Clear Log</button>
        </div>

        <div>
            <h3>Test Log:</h3>
            <div id="log" class="log">Ready to test...\n</div>
        </div>
    </div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://oijoqbyfwbyochcxdutg.supabase.co'
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9pam9xYnlmd2J5b2NoY3hkdXRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NTY0MzgsImV4cCI6MjA2NTEzMjQzOH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey)
        let currentUser = null

        function log(message, type = 'info') {
            const logElement = document.getElementById('log')
            const timestamp = new Date().toLocaleTimeString()
            const className = type
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`
            logElement.scrollTop = logElement.scrollHeight
            console.log(message)
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 'Log cleared...\n'
        }

        async function signIn() {
            try {
                log('🔐 Attempting to sign in...', 'info')
                
                // Try to get existing session first
                const { data: { session }, error: sessionError } = await supabase.auth.getSession()
                
                if (session) {
                    currentUser = session.user
                    log(`✅ Already signed in as: ${currentUser.email}`, 'success')
                    document.getElementById('paymentBtn').disabled = false
                    document.getElementById('statusBtn').disabled = false
                    return
                }
                
                // Try to sign in with a test user
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: 'password123' // You'll need to provide the correct password
                })
                
                if (error) {
                    log(`❌ Sign in failed: ${error.message}`, 'error')
                    log('💡 Please update the email/password in the script or create a test user', 'warning')
                    return
                }
                
                currentUser = data.user
                log(`✅ Signed in successfully as: ${currentUser.email}`, 'success')
                document.getElementById('paymentBtn').disabled = false
                document.getElementById('statusBtn').disabled = false
                
            } catch (error) {
                log(`❌ Sign in error: ${error.message}`, 'error')
            }
        }

        async function testPayment() {
            if (!currentUser) {
                log('❌ Please sign in first', 'error')
                return
            }

            try {
                log('💳 Initiating payment test...', 'info')
                log(`👤 User: ${currentUser.id}`, 'info')
                
                const { data, error } = await supabase.functions.invoke('pesapal-initiate', {
                    body: {
                        amount: 10,
                        currency: 'KES'
                    }
                })

                log(`📡 Function response received`, 'info')
                log(`📊 Data: ${JSON.stringify(data, null, 2)}`, 'info')
                
                if (error) {
                    log(`❌ Function error: ${JSON.stringify(error, null, 2)}`, 'error')
                    return
                }

                if (data?.success && data?.redirect_url) {
                    log(`✅ Payment initiated successfully!`, 'success')
                    log(`🔗 Redirect URL: ${data.redirect_url}`, 'success')
                    log(`📋 Tracking ID: ${data.tracking_id}`, 'info')
                    log(`📋 Merchant Reference: ${data.merchant_reference}`, 'info')
                    
                    // Open payment window
                    log(`🌐 Opening payment window...`, 'info')
                    window.open(data.redirect_url, '_blank', 'width=800,height=600')
                } else {
                    log(`❌ Payment initiation failed: ${JSON.stringify(data)}`, 'error')
                }

            } catch (error) {
                log(`❌ Payment test error: ${error.message}`, 'error')
            }
        }

        async function checkStatus() {
            if (!currentUser) {
                log('❌ Please sign in first', 'error')
                return
            }

            try {
                log('🔍 Checking payment status...', 'info')
                
                // Query the database for recent payments
                const { data: payments, error } = await supabase
                    .from('user_payments')
                    .select('*')
                    .eq('user_id', currentUser.id)
                    .order('created_at', { ascending: false })
                    .limit(5)

                if (error) {
                    log(`❌ Database query error: ${error.message}`, 'error')
                    return
                }

                log(`📊 Found ${payments.length} payment records:`, 'info')
                payments.forEach((payment, index) => {
                    log(`${index + 1}. ID: ${payment.id}`, 'info')
                    log(`   Amount: ${payment.amount} ${payment.currency}`, 'info')
                    log(`   Status: ${payment.status}`, payment.status === 'completed' ? 'success' : 'warning')
                    log(`   Tracking ID: ${payment.pesapal_tracking_id || 'None'}`, 'info')
                    log(`   Created: ${new Date(payment.created_at).toLocaleString()}`, 'info')
                    log(`   ---`, 'info')
                })

                // Check user settings
                const { data: settings } = await supabase
                    .from('user_settings')
                    .select('has_paid')
                    .eq('user_id', currentUser.id)
                    .single()

                log(`💰 User has paid: ${settings?.has_paid ? 'Yes' : 'No'}`, settings?.has_paid ? 'success' : 'warning')

            } catch (error) {
                log(`❌ Status check error: ${error.message}`, 'error')
            }
        }

        // Auto-check for existing session on load
        window.addEventListener('load', () => {
            log('🚀 Test page loaded. Click "Sign In" to start testing.', 'info')
            signIn()
        })
    </script>
</body>
</html>
