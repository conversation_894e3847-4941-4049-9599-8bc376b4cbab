<!DOCTYPE html>
<html>
<head>
    <title>Test Pesapal Function</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test Pesapal Payment Function</h1>
    <button onclick="testPayment()">Test Payment</button>
    <div id="output"></div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://oijoqbyfwbyochcxdutg.supabase.co'
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9pam9xYnlmd2J5b2NoY3hkdXRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NTY0MzgsImV4cCI6MjA2NTEzMjQzOH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey)

        function log(message) {
            const output = document.getElementById('output')
            output.innerHTML += '<p>' + JSON.stringify(message, null, 2) + '</p>'
            console.log(message)
        }

        async function testPayment() {
            try {
                log('Starting payment test...')
                
                // First, let's try to sign in with a test user or check current session
                const { data: { session }, error: sessionError } = await supabase.auth.getSession()
                
                if (sessionError) {
                    log('Session error: ' + sessionError.message)
                    return
                }
                
                if (!session) {
                    log('No active session. Please sign in first.')
                    // Try to sign in with test credentials
                    const { data, error } = await supabase.auth.signInWithPassword({
                        email: '<EMAIL>',
                        password: 'testpassword'
                    })
                    
                    if (error) {
                        log('Sign in error: ' + error.message)
                        return
                    }
                    
                    log('Signed in successfully')
                } else {
                    log('Active session found for user: ' + session.user.id)
                }

                // Now test the payment function
                log('Calling pesapal-initiate function...')
                
                const { data, error } = await supabase.functions.invoke('pesapal-initiate', {
                    body: {
                        amount: 0.10,
                        currency: 'USD'
                    }
                })

                if (error) {
                    log('Function error: ' + error.message)
                    log('Full error object: ' + JSON.stringify(error))
                } else {
                    log('Function response: ' + JSON.stringify(data))
                }

            } catch (error) {
                log('Caught error: ' + error.message)
                log('Full error: ' + JSON.stringify(error))
            }
        }
    </script>
</body>
</html>
