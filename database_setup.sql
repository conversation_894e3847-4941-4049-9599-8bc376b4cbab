-- Simple Database Setup for Inventory
-- Run this in Supabase SQL Editor

-- First, let's disable <PERSON><PERSON> temporarily to avoid conflicts
ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions DISABLE ROW LEVEL SECURITY;

-- Clear any existing policies for products
DROP POLICY IF EXISTS "Enable read access for users based on user_id" ON public.products;
DROP POLICY IF EXISTS "Enable insert for users based on user_id" ON public.products;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.products;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON public.products;

-- Clear any existing policies for transactions
DROP POLICY IF EXISTS "Enable read access for users based on user_id" ON public.transactions;
DROP POLICY IF EXISTS "Enable insert for users based on user_id" ON public.transactions;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.transactions;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON public.transactions;

-- Clear all products to start fresh (optional - uncomment if needed)
-- DELETE FROM public.products;
-- DELETE FROM public.transactions;

-- Re-enable RLS for both tables
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

-- Create simple, working policies for products
CREATE POLICY "Enable read access for users based on user_id" ON public.products
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Enable insert for users based on user_id" ON public.products
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update for users based on user_id" ON public.products
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Enable delete for users based on user_id" ON public.products
    FOR DELETE USING (auth.uid() = user_id);

-- Create simple, working policies for transactions
CREATE POLICY "Enable read access for users based on user_id" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Enable insert for users based on user_id" ON public.transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update for users based on user_id" ON public.transactions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Enable delete for users based on user_id" ON public.transactions
    FOR DELETE USING (auth.uid() = user_id);

-- Test the setup
SELECT 'Database setup complete!' as status;
