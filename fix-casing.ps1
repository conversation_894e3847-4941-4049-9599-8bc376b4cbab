# PowerShell script to fix file casing issues in the project

# Rename files to match the correct casing
Rename-Item -Path ".\src\pages\expenses.tsx" -NewName "Expenses.tsx" -Force
Rename-Item -Path ".\src\pages\inventory.tsx" -NewName "Inventory.tsx" -Force
Rename-Item -Path ".\src\pages\sales.tsx" -NewName "Sales.tsx" -Force
Rename-Item -Path ".\src\pages\services.tsx" -NewName "Services.tsx" -Force

Write-Host "File casing has been fixed. Please restart your development server if it's running." -ForegroundColor Green
