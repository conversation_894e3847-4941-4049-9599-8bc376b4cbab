// Test script to verify your actual Pesapal credentials
// This will help us identify if the issue is with the credentials

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://oijoqbyfwbyochcxdutg.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9pam9xYnlmd2J5b2NoY3hkdXRnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTU1NjQzOCwiZXhwIjoyMDY1MTMyNDM4fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // This is the anon key, not service key

async function testActualCredentials() {
  try {
    console.log('Testing your actual Pesapal credentials...');
    
    // We'll simulate what the edge function does
    // Since we can't access the encrypted environment variables directly,
    // let's test by calling the actual function
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // First, let's try to get a user token to test the function
    console.log('Testing function call...');
    
    // Create a test user session (you'll need to replace this with actual user credentials)
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>', // Use one of your test users
      password: 'your-password-here' // You'll need to provide the password
    });
    
    if (authError) {
      console.error('Auth error:', authError);
      console.log('Skipping function test due to auth error');
      return;
    }
    
    console.log('Authenticated successfully');
    
    // Now test the function
    const { data, error } = await supabase.functions.invoke('pesapal-initiate', {
      body: {
        amount: 10,
        currency: 'KES'
      }
    });
    
    console.log('Function response:', { data, error });
    
    if (error) {
      console.error('Function error details:', error);
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Alternative test - direct credential test
async function testCredentialsDirectly() {
  console.log('\n=== Testing credentials directly ===');
  
  // These would be your actual credentials - you'll need to provide them
  // For security, don't commit these to version control
  const YOUR_CONSUMER_KEY = 'your-actual-consumer-key-here';
  const YOUR_CONSUMER_SECRET = 'your-actual-consumer-secret-here';
  
  if (YOUR_CONSUMER_KEY === 'your-actual-consumer-key-here') {
    console.log('Please replace YOUR_CONSUMER_KEY and YOUR_CONSUMER_SECRET with your actual credentials');
    console.log('You can find these in your Pesapal merchant account email');
    return;
  }
  
  try {
    console.log('Testing token request with your credentials...');
    
    const tokenResponse = await fetch('https://cybqa.pesapal.com/pesapalv3/api/Auth/RequestToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        consumer_key: YOUR_CONSUMER_KEY,
        consumer_secret: YOUR_CONSUMER_SECRET
      })
    });

    console.log('Token response status:', tokenResponse.status);
    const tokenData = await tokenResponse.json();
    console.log('Token response:', tokenData);
    
    if (tokenData.token) {
      console.log('✅ Your credentials are working!');
    } else {
      console.log('❌ Your credentials are not working');
      console.log('Please check:');
      console.log('1. Are these the correct credentials from your Pesapal merchant account?');
      console.log('2. Are you using sandbox credentials with the sandbox URL?');
      console.log('3. Are the credentials properly formatted (no extra spaces, etc.)?');
    }
    
  } catch (error) {
    console.error('Direct test failed:', error);
  }
}

// Run the tests
console.log('=== Pesapal Credentials Test ===');
testActualCredentials().then(() => {
  testCredentialsDirectly();
});
